import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
import { Link, useLocation } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCog, faPlug, faDatabase, faBoxOpen, faSearch, faUserCog,
  faBell, faPalette, faShieldAlt, faBook, faChevronLeft, faRobot, faPlus, faUser,
  faFolder, faFile, faRefresh, faExclamationTriangle, faDownload, faTrash, faServer,
  faHardDrive, faHome, faChevronRight
} from '@fortawesome/free-solid-svg-icons';

import { Button } from "../components/ui/button.jsx";
import { Input } from "../components/ui/input.jsx";
import { <PERSON>, CardHeader, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, CardDescription, CardFooter } from "../components/ui/card.jsx";
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from "../components/ui/tabs.jsx";
import { Switch } from "../components/ui/switch.jsx";
import { Label } from "../components/ui/label.jsx";
import StorageSettings from '../components/StorageSettings.jsx';
import SimpleFolderSelector from '../components/SimpleFolderSelector.jsx';
import ServerProfileCard from '../components/ServerProfileCard.jsx';
import ModelFileViewer from '../components/ModelFileViewer.jsx';
import ModelProfileCard from '../components/ModelProfileCard.jsx';
import ModelDownloadStatus from '../components/ModelDownloadStatus.jsx';
import { useSettings } from '../contexts/SettingsContext.jsx';
import StatusBar from '../components/StatusBar.jsx';
import FloatingChat from '../components/FloatingChat.jsx';
import FileManager from '../components/FileManager.jsx';

const Settings = () => {
  const { userSettings, saveUserSetting, updateUserSettings, isLoading: settingsLoading, error: settingsError } = useSettings();
  const [activeMainTab, setActiveMainTab] = useState('System');
  const [loadingPlugins, setLoadingPlugins] = useState(false);
  const [loadingMigration, setLoadingMigration] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('General');
  const [searchTerm, setSearchTerm] = useState('');
  const [plugins, setPlugins] = useState([]);
  const [pluginError, setPluginError] = useState(null);
  const [pluginsDirectoryContents, setPluginsDirectoryContents] = useState([]);
  const location = useLocation();

  // Top-level loading and error fallback
  if (settingsLoading) {
    return (
      <div className="flex items-center justify-center w-full h-full bg-background text-foreground">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-foreground mb-2">Settings</h2>
          <p className="text-muted-foreground">Loading settings...</p>
        </div>
      </div>
    );
  }
  if (settingsError) {
    return (
      <div className="flex items-center justify-center w-full h-full bg-background text-foreground">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-destructive mb-2">Settings Error</h2>
          <p className="text-muted-foreground mb-4">Failed to load settings: {settingsError}</p>
          <p className="text-sm text-muted-foreground">Using default settings...</p>
        </div>
      </div>
    );
  }

  // Remove configBaseUrl - we'll use Tauri commands instead

  // Ollama states
  const [serverStatus, setServerStatus] = useState('stopped'); // Start with stopped instead of unknown
  const [serverModels, setServerModels] = useState([]);
  const [loadingServerModels, setLoadingServerModels] = useState(false);
  const [modelToPull, setModelToPull] = useState('');
  const [pullingModel, setPullingModel] = useState(false);
  const [pullModelMessage, setPullModelMessage] = useState('');
  const [pullModelError, setPullModelError] = useState('');
  const [downloadStatus, setDownloadStatus] = useState(null);
  const [serverDirectoryContents, setServerDirectoryContents] = useState([]);
  const [modelDirectoryContents, setModelDirectoryContents] = useState([]);

  // Server and Model profile states
  const [serverProfiles, setServerProfiles] = useState([]);
  const [modelProfiles, setModelProfiles] = useState([]);
  const [activeServerProfile, setActiveServerProfile] = useState('');
  const [activeModelProfile, setActiveModelProfile] = useState('');
  const [loadingProfiles, setLoadingProfiles] = useState(false);

  // Logic files are now handled by FileManager component

  useEffect(() => {
    const unlistenStart = listen('model_pull_start', (event) => {
      setDownloadStatus({
        modelName: event.payload,
        status: 'starting',
        total: 0,
        completed: 0,
      });
      setPullingModel(true);
      setPullModelMessage('');
      setPullModelError('');
    });

    const unlistenProgress = listen('model_pull_progress', (event) => {
      console.log('Progress event received:', event.payload);
      const payload = event.payload;

      // Handle Ollama's progress format
      if (payload.status && payload.total && payload.completed !== undefined) {
        setDownloadStatus({
          modelName: modelToPull || 'Unknown Model',
          status: payload.status,
          total: payload.total,
          completed: payload.completed
        });
      }
    });

    const unlistenSuccess = listen('model_pull_success', (event) => {
      setDownloadStatus(null);
      setPullingModel(false);
      setPullModelMessage(`Successfully pulled ${event.payload}`);
      // Refresh both Ollama models and model profiles
      fetchOllamaModels();
      loadModelProfiles();
      setModelToPull(''); // Clear the input
    });

    const unlistenError = listen('model_pull_error', (event) => {
      setDownloadStatus(null);
      setPullingModel(false);
      setPullModelError(`Failed to pull model: ${event.payload}`);
    });

    return () => {
      unlistenStart.then(f => f());
      unlistenProgress.then(f => f());
      unlistenSuccess.then(f => f());
      unlistenError.then(f => f());
    };
  }, []);

  // Fetch Ollama status on mount (server should already be started by App.jsx)
  useEffect(() => {
    console.log('Settings page: Fetching server status...');
    fetchServerStatus();
  }, []);

  const fetchServerStatus = async () => {
    console.log('=== FRONTEND: Fetching server status ===');
    try {
      const status = await invoke('get_server_status');
      console.log('Raw status response:', status);
      setServerStatus(status.status);
      console.log('Server status set to:', status.status);
    } catch (error) {
      console.error('Error fetching server status:', error);
      setServerStatus('error');
    }
  };
  

  const handleStartServer = async () => {
    console.log('=== FRONTEND: Start button clicked ===');
    try {
      setServerStatus('starting');
      console.log('Starting server...');
      console.log('About to invoke start_server...');
      const result = await invoke('start_server_command');
      console.log('Server start command completed, result:', result);
      // Wait a moment then check status
      setTimeout(() => {
        fetchServerStatus();
      }, 3000); // Increased wait time
    } catch (error) {
      console.error('Error starting server:', error);
      setServerStatus('error');
      // Show the error to the user
      alert(`Failed to start server:\n\n${error}`);
    }
  };



  const handleStopServer = async () => {
    try {
      console.log('Stopping server...');
      await invoke('stop_server_command');
      console.log('Server stop command completed');
      setServerStatus('stopped');
      setTimeout(() => {
        fetchServerStatus();
      }, 1000);
    } catch (error) {
      console.error('Error stopping server:', error);
      alert(`Failed to stop server:\n\n${error}`);
      fetchServerStatus();
    }
  };

  const handleCheckInstallation = async () => {
    try {
      const result = await invoke('check_installation_command');
      alert(`Installation Check:\n\n${result}`);
    } catch (error) {
      alert(`Installation Check Failed:\n\n${error}`);
    }
  };

  const fetchServerModels = async () => {
    setLoadingServerModels(true);
    try {
      const models = await invoke('get_models_command');
      setServerModels(models);
    } catch (error) {
      console.error('Error fetching server models:', error);
      setServerModels([]);
    } finally {
      setLoadingServerModels(false);
    }
  };

  const handlePullModel = async () => {
    if (!modelToPull) return;
    console.log('Starting model pull for:', modelToPull);
    setPullingModel(true);
    setPullModelMessage('');
    setPullModelError('');
    setDownloadStatus({
      modelName: modelToPull,
      status: 'initializing',
      total: 0,
      completed: 0
    });

    try {
      await invoke('pull_model_command', { model_name: modelToPull });
      console.log('Model pull command sent successfully');
    } catch (error) {
      console.error('Error pulling model:', error);
      setPullModelError(`Failed to pull model: ${error}`);
      setDownloadStatus(null);
      setPullingModel(false);
    }
  };



  const mainTabsConfig = [
    { name: 'System', icon: faUserCog },
    { name: 'Addons', icon: faPlug } // Renamed Plugins to Addons
  ];

  const sidebarItemsConfig = {
    System: [
      { name: 'General', icon: faUserCog },
      { name: 'User Preferences', icon: faUser },
      { name: 'Models/Assistant', icon: faRobot },
      { name: 'Logic', icon: faCog },
      { name: 'Extensions', icon: faPlug },
      { name: 'Notification Preferences', icon: faBell },
      { name: 'Chat History', icon: faBook },
      { name: 'Appearance', icon: faPalette },
      { name: 'Data Controls', icon: faShieldAlt },
      { name: 'Storage', icon: faDatabase },
      { name: 'System Log', icon: faBook },
      { name: 'Docket', icon: faBoxOpen }
    ],
    Addons: [
      { name: 'Plugins', icon: faPlug },
      { name: 'MCP', icon: faCog }, // MCP (Model Context Protocol) remains under Addons
      { name: 'APIs', icon: faBell }
    ]
    // Removed Storage and Docket main tab configurations
  };

  // Initialize all data on component mount (no more lazy loading)
  useEffect(() => {
    const initializeAllData = async () => {
      console.log('🚀 Settings: Initializing all data on mount');

      // Load all data regardless of active tab
      try {
        await Promise.all([
          fetchOllamaStatus(),
          fetchServerModels(),
          loadServerProfiles(),
          // loadModelProfiles(), // Removed - using direct server queries
          fetchPlugins()
        ]);
        console.log('✅ Settings: All data initialized');
      } catch (error) {
        console.error('❌ Settings: Failed to initialize data:', error);
      }
    };

    initializeAllData();
  }, []); // Only run once on mount

  // Load Logic files when the component mounts and whenever the Logic path is available
  // This is now handled by FileManager component automatically
  useEffect(() => {
    // The FileManager will handle file loading automatically via useEffect
  }, []);

  const loadServerProfiles = async () => {
    console.log('=== FRONTEND: Loading server profiles ===');
    try {
      setLoadingProfiles(true);
      const profiles = await invoke('get_server_profiles');
      console.log('Raw server profiles response:', profiles);
      setServerProfiles(profiles);
      console.log('Server profiles set to:', profiles);
      
      // Load active server profile
      try {
        const activeProfile = await invoke('get_active_server_profile');
        setActiveServerProfile(activeProfile?.name || '');
        console.log('Active server profile:', activeProfile?.name || 'None');
      } catch (error) {
        console.warn('No active server profile set:', error);
        setActiveServerProfile('');
      }
    } catch (error) {
      console.error('Error loading server profiles:', error);
    } finally {
      setLoadingProfiles(false);
    }
  };

  const loadModelProfiles = async () => {
    try {
      const profiles = await invoke('get_model_profiles');
      setModelProfiles(profiles);
      console.log('Loaded model profiles:', profiles);
      
      // Load active model profile
      try {
        const activeProfile = await invoke('get_active_model_profile');
        setActiveModelProfile(activeProfile?.name || '');
        console.log('Active model profile:', activeProfile?.name || 'None');
      } catch (error) {
        console.warn('No active model profile set:', error);
        setActiveModelProfile('');
      }
    } catch (error) {
      console.error('Error loading model profiles:', error);
    }
  };

  const handleToggleServer = async (serverName, enabled) => {
    try {
      await invoke('toggle_server_profile', { serverName, enabled });
      loadServerProfiles(); // Refresh the list
    } catch (error) {
      console.error('Error toggling server:', error);
    }
  };

  const handleToggleModel = async (modelName, enabled) => {
    try {
      await invoke('toggle_model_profile', { modelName, enabled });
      loadModelProfiles(); // Refresh the list
    } catch (error) {
      console.error('Error toggling model:', error);
    }
  };

  const handleSetActiveServer = async (serverName) => {
    try {
      await invoke('set_active_server_profile', { serverName });
      setActiveServerProfile(serverName);
      console.log('Set active server profile:', serverName);
    } catch (error) {
      console.error('Error setting active server:', error);
      alert(`Failed to set active server: ${error}`);
    }
  };

  const handleSetActiveModel = async (modelName) => {
    try {
      await invoke('set_active_model_profile', { modelName });
      setActiveModelProfile(modelName);
      console.log('Set active model profile:', modelName);
    } catch (error) {
      console.error('Error setting active model:', error);
      alert(`Failed to set active model: ${error}`);
    }
  };

  const handleRefreshServer = async (serverName) => {
    try {
      await invoke('refresh_server_profile_command', { serverName });
      loadServerProfiles(); // Refresh the list
    } catch (error) {
      console.error('Error refreshing server profile:', error);
      alert(`Failed to refresh server profile: ${error}`);
    }
  };

  const handleDeleteServer = async (serverName) => {
    if (window.confirm(`Are you sure you want to delete the server profile "${serverName}"?`)) {
      try {
        await invoke('delete_server_profile', { serverName });
        loadServerProfiles(); // Refresh the list
      } catch (error) {
        console.error('Error deleting server profile:', error);
        alert(`Failed to delete server profile: ${error}`);
      }
    }
  };

  const handleDeleteModel = async (modelName) => {
    if (window.confirm(`Are you sure you want to delete the model profile "${modelName}"?`)) {
      try {
        await invoke('delete_model_profile', { model_name: modelName });
        loadModelProfiles(); // Refresh the list
      } catch (error) {
        console.error('Error deleting model profile:', error);
        alert(`Failed to delete model profile: ${error}`);
      }
    }
  };



  // Test function to check if tauri invoke is working
  const testTauriConnection = async () => {
    console.log('=== TESTING TAURI CONNECTION ===');
    try {
      const result = await invoke('greet', { name: 'Test' });
      console.log('Greet result:', result);
      alert(`Tauri connection works! Result: ${result}`);
    } catch (error) {
      console.error('Tauri connection failed:', error);
      alert(`Tauri connection failed: ${error}`);
    }
  };

  const handleServerPathSelected = async (path) => {
    console.log('Server path selected:', path);
    await listAndDisplayDirectoryContents(path, setServerDirectoryContents);
    // Load server profiles after path is selected
    loadServerProfiles();
  };

  const handleModelPathSelected = async (path) => {
    console.log('Model path selected:', path);
    await listAndDisplayDirectoryContents(path, setModelDirectoryContents);
    // Load model profiles after path is selected
    loadModelProfiles();
  };

  const handleStoragePathSelected = (path) => {
    console.log('Storage path selected:', path);
  };

  const handleSystemPromptsPathSelected = (path) => {
    console.log('System prompts path selected:', path);
    // The FileManager will handle file loading automatically via useEffect
  };

  // Logic file loading is now handled by FileManager component

  const handlePluginsPathSelected = async (path) => {
    console.log('Plugins path selected:', path);
    // Trigger a refresh of the plugin list here
    await fetchPlugins();
    // Also list the contents of the selected directory
    await listAndDisplayDirectoryContents(path);
  };

  const listAndDisplayDirectoryContents = async (path) => {
    try {
      // Use browse_directory instead of the non-existent list_directory_contents
      const result = await invoke('browse_directory', { path });
      // Convert browse_directory format to the expected format
      const contents = [
        ...(result.directories || []).map(dir => ({ 
          name: dir.name || dir, 
          path: dir.path || `${path}\\${dir.name || dir}`, 
          is_dir: true 
        })),
        ...(result.files || []).map(file => ({ 
          name: file.name || file, 
          path: file.path || `${path}\\${file.name || file}`, 
          is_dir: false 
        }))
      ];
      setPluginsDirectoryContents(contents);
    } catch (error) {
      console.error('Error listing directory contents:', error);
      setPluginsDirectoryContents([]);
    }
  };

  const fetchPlugins = async () => {
    try {
      setLoadingPlugins(true);
      const pluginsList = await invoke('get_plugins');
      setPlugins(pluginsList);
      setPluginError(null);
    } catch (err) {
      console.error('Error fetching plugins:', err);
      setPluginError('Failed to load plugins. Make sure plugins path is configured and contains valid plugins.');
    } finally {
      setLoadingPlugins(false);
    }
  };

  const togglePlugin = async (pluginName, currentStatus) => {
    try {
      const newStatus = !currentStatus;
      await invoke('toggle_plugin', { plugin_name: pluginName, enabled: newStatus });
      // Update local state to reflect the change
      setPlugins(plugins.map(p => p.name === pluginName ? { ...p, enabled: newStatus } : p));
    } catch (err) {
      console.error(`Error toggling plugin ${pluginName}:`, err);
      setPluginError(`Failed to update plugin status for ${pluginName}.`);
    }
  };

  const handleMainTabChange = (value) => {
    setActiveMainTab(value);
    if (sidebarItemsConfig[value] && sidebarItemsConfig[value].length > 0) {
      setActiveSidebarItem(sidebarItemsConfig[value][0].name);
    } else {
      setActiveSidebarItem('');
    }
  };

  const currentSidebarItems = sidebarItemsConfig[activeMainTab] || [];
  const filteredSidebarItems = currentSidebarItems.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Server Status and Model Pull Section (Top Priority)
  const renderServerStatusSection = () => (
    <div className="space-y-6 mb-8">
      {/* Server Status & Control */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <div className={`w-4 h-4 rounded-full ${
              serverStatus === 'running' ? 'bg-green-500 animate-pulse' :
              serverStatus === 'starting' ? 'bg-yellow-500 animate-pulse' :
              'bg-red-500'
            }`}></div>
            <span>AI Server Status</span>
          </CardTitle>
          <CardDescription>
            Current Status: {
              serverStatus === 'running' ? 'Running and Ready' :
              serverStatus === 'starting' ? 'Starting Up...' :
              serverStatus === 'stopped' ? 'Stopped' :
              serverStatus === 'no_server_profile' ? 'No Server Profile Configured' :
              'Error or Unknown'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-2">
            <Button
              onClick={handleStartServer}
              size="sm"
              variant="default"
              disabled={serverStatus === 'running' || serverStatus === 'starting' || serverStatus === 'no_server_profile'}
              className="flex-1"
            >
              {serverStatus === 'starting' ? 'Starting...' : 'Start Server'}
            </Button>
            <Button
              onClick={handleStopServer}
              size="sm"
              variant="destructive"
              disabled={serverStatus === 'stopped' || serverStatus === 'no_server_profile' || serverStatus === 'error'}
              className="flex-1"
            >
              Stop Server
            </Button>
            <Button onClick={fetchServerStatus} size="sm" variant="outline">
              Refresh Status
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Model Pull Section */}
      <Card>
        <CardHeader>
          <CardTitle>Download AI Models</CardTitle>
          <CardDescription>Pull and download AI models for use with your server</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Model Pull Input */}
          <div className="flex space-x-2">
            <Input
              value={modelToPull}
              onChange={(e) => setModelToPull(e.target.value)}
              placeholder="e.g. qwen2:0.5b, llama3, codellama"
              disabled={pullingModel || serverStatus !== 'running'}
              className={serverStatus !== 'running' ? 'opacity-50' : ''}
            />
            <Button
              onClick={handlePullModel}
              disabled={pullingModel || !modelToPull || serverStatus !== 'running'}
              className="min-w-[100px]"
            >
              {pullingModel ? 'Pulling...' : 'Pull Model'}
            </Button>
          </div>

          {/* Status Messages */}
          {serverStatus !== 'running' && (
            <div className="p-3 rounded-lg bg-yellow-50 border border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800">
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                ⚠️ Server must be running to pull models.
                {serverStatus === 'stopped' && ' Click "Start Server" above to begin.'}
                {serverStatus === 'no_server_profile' && ' Please configure a server profile first.'}
              </p>
            </div>
          )}

          <div className="text-xs text-gray-500">
            Popular models: qwen2:0.5b, llama3, codellama, mistral, phi3
          </div>

          <ModelDownloadStatus downloadStatus={downloadStatus} />
          {pullModelMessage && (
            <div className="p-3 rounded-lg bg-green-50 border border-green-200 dark:bg-green-900/20 dark:border-green-800">
              <p className="text-sm text-green-800 dark:text-green-200">✅ {pullModelMessage}</p>
            </div>
          )}
          {pullModelError && (
            <div className="p-3 rounded-lg bg-red-50 border border-red-200 dark:bg-red-900/20 dark:border-red-800">
              <p className="text-sm text-red-800 dark:text-red-200">❌ {pullModelError}</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );

  const renderOllamaSettings = () => (
    <div className="space-y-6">
      {/* Server Status and Model Pull Section at the top */}
      {renderServerStatusSection()}

      {/* Configuration Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Server Management Card */}
      <Card>
        <CardHeader>
          <CardTitle>Server Management</CardTitle>
          <CardDescription>Manage your AI server instances.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <SimpleFolderSelector
            label="Server Path"
            description="Select the directory where the server executable is located."
            fetchCommand="get_servers_path"
            saveCommand="set_servers_path"
            onFolderSelected={handleServerPathSelected}
          />
          {serverDirectoryContents.length > 0 && (
            <div className="mt-4">
              <h4 className="text-md font-semibold mb-2">Server Directory Contents:</h4>
              <ul className="list-disc pl-5">
                {serverDirectoryContents.map((entry, index) => (
                  <li key={index} className="text-sm">
                    {entry.name} {entry.is_dir ? '/' : ''}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Server Profile Cards */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <h4 className="text-md font-semibold">Available Servers:</h4>
              <Button onClick={loadServerProfiles} size="sm" variant="outline">
                <FontAwesomeIcon icon={faRefresh} className="mr-2 h-4 w-4" />
                Refresh
              </Button>
            </div>
            <p className="text-xs text-gray-500 mb-2">
              New servers are detected by adding folders to the server path.
            </p>
            {loadingProfiles ? (
              <div className="text-center py-2">Loading...</div>
            ) : serverProfiles.length > 0 ? (
              <div className="space-y-2">
                {serverProfiles.map((profile, index) => (
                  <ServerProfileCard
                    key={index}
                    profile={profile}
                    onToggleEnabled={(enabled) => handleToggleServer(profile.name, enabled)}
                    onDelete={() => handleDeleteServer(profile.name)}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-2 text-gray-500 text-sm">
                No server folders found
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Model Management Card */}
      <Card>
        <CardHeader>
          <CardTitle>Model Management</CardTitle>
          <CardDescription>Manage your AI model storage and downloaded models.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <SimpleFolderSelector
            label="Model Storage Path"
            description="Select the directory where AI models are stored."
            fetchCommand="get_models_path"
            saveCommand="set_models_path"
            onFolderSelected={handleModelPathSelected}
          />
          <ModelFileViewer
            modelPath={userSettings.models_path || "E:\\TheCollective\\Storage\\System\\Models"}
            onRefresh={() => handleModelPathSelected(userSettings.models_path || "E:\\TheCollective\\Storage\\System\\Models")}
          />

          {/* Model Profile Cards */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <h4 className="text-md font-semibold">Available Models:</h4>
              <Button onClick={loadModelProfiles} size="sm" variant="outline">
                <FontAwesomeIcon icon={faRefresh} className="mr-2 h-4 w-4" />
                Refresh
              </Button>
            </div>
            <p className="text-xs text-gray-500 mb-2">
              New models are detected by adding folders to the model storage path.
            </p>
            {modelProfiles.length > 0 ? (
              <div className="space-y-2">
                {modelProfiles.map((profile, index) => (
                  <ModelProfileCard
                    key={index}
                    profile={profile}
                    onToggleEnabled={(enabled) => handleToggleModel(profile.name, enabled)}
                    onDelete={() => handleDeleteModel(profile.name)}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-2 text-gray-500 text-sm">
                No model folders found
              </div>
            )}
          </div>

        </CardContent>
      </Card>
      </div>
    </div>
  );

  const renderContent = () => {
    console.log('renderContent called with activeSidebarItem:', activeSidebarItem);
    if (!activeSidebarItem) {
      return (
        <Card className="m-6">
          <CardContent className="p-6">
            <p className="text-muted-foreground">Please select a settings category from the sidebar.</p>
          </CardContent>
        </Card>
      );
    }
    if (!activeSidebarItem) {
      return (
        <div className="flex flex-col items-center justify-center h-full text-center text-muted-foreground p-8">
          <FontAwesomeIcon icon={faCog} className="h-16 w-16 mb-6 text-primary" />
          <h2 className="text-2xl font-semibold mb-2">Select a setting</h2>
          <p>Choose an item from the sidebar to view or modify its settings.</p>
        </div>
      );
    }

    switch (`${activeMainTab}-${activeSidebarItem}`) {
      case 'System-General':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
              <CardDescription>Basic application and backend settings.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">


              {/* About Section */}
              <div className="p-4 border rounded-lg">
                <h4 className="font-medium">About The Collective</h4>
                <p className="text-sm text-muted-foreground">Version 0.1.0 - A PC-based AI assistant with modular functionality.</p>
              </div>
            </CardContent>
          </Card>
        );
      case 'System-User Preferences':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>User Preferences</CardTitle>
              <CardDescription>Manage your personal settings and preferences stored in ./storage/System/User/config.json</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Dark Mode Toggle */}
              <div className="p-4 border rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Dark Mode</h4>
                    <p className="text-sm text-muted-foreground">Toggle between light and dark themes</p>
                  </div>
                  <Switch
                    checked={userSettings?.dark_mode || false}
                    onCheckedChange={(checked) => saveUserSetting('dark_mode', checked)}
                  />
                </div>
              </div>

              {/* Layout Setting */}
              <div className="p-4 border rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Layout</h4>
                    <p className="text-sm text-muted-foreground">Current layout: {userSettings?.layout || 'default'}</p>
                  </div>
                  <Input
                    value={userSettings?.layout || 'default'}
                    onChange={(e) => saveUserSetting('layout', e.target.value)}
                    className="w-32"
                    placeholder="default"
                  />
                </div>
              </div>

              {/* Theme Setting */}
              <div className="p-4 border rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Theme</h4>
                    <p className="text-sm text-muted-foreground">Current theme: {userSettings?.theme || 'light'}</p>
                  </div>
                  <Input
                    value={userSettings?.theme || 'light'}
                    onChange={(e) => saveUserSetting('theme', e.target.value)}
                    className="w-32"
                    placeholder="light"
                  />
                </div>
              </div>

              {/* System Log Path */}
              <div className="p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">System Log Path</h4>
                  <p className="text-sm text-muted-foreground mb-2">Directory where system logs are stored</p>
                  <Input
                    value={userSettings?.system_log_path || './Storage/System/logs'}
                    onChange={(e) => saveUserSetting('system_log_path', e.target.value)}
                    placeholder="./Storage/System/logs"
                  />
                </div>
              </div>

              {/* Plugin States Info */}
              <div className="p-4 border rounded-lg">
                <h4 className="font-medium">Plugin States</h4>
                <p className="text-sm text-muted-foreground">
                  {userSettings?.plugin_states && Object.keys(userSettings.plugin_states).length > 0
                    ? `${Object.keys(userSettings.plugin_states).length} plugin states stored`
                    : 'No plugin states configured'}
                </p>
              </div>
            </CardContent>
          </Card>
        );
      case 'System-Models/Assistant':
        return renderOllamaSettings();
      case 'System-Appearance': // Added Appearance section content
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>Appearance</CardTitle>
              <CardDescription>Customize the look and feel of the application.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Theme</h4>
                  <p className="text-sm text-muted-foreground">Switch between light and dark mode.</p>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={userSettings?.dark_mode || false}
                    onCheckedChange={(checked) => saveUserSetting('dark_mode', checked)}
                  />
                  <Label htmlFor="theme-toggle">
                    {userSettings?.dark_mode ? 'Dark Mode' : 'Light Mode'}
                  </Label>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      case 'System-Storage':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>Storage Settings</CardTitle>
              <CardDescription>Manage the directory to be indexed for searching.</CardDescription>
            </CardHeader>
            <CardContent>
              <SimpleFolderSelector
                label="Indexed Directory"
                description="Select the directory that the assistant should scan and index for files. Should be ./Storage/ for portable operation."
                fetchCommand="get_indexed_directory"
                saveCommand="set_indexed_directory"
                onFolderSelected={handleStoragePathSelected}
              />
            </CardContent>
          </Card>
        );
      case 'System-System Log':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>System Log Settings</CardTitle>
              <CardDescription>Configure system log storage and behavior.</CardDescription>
            </CardHeader>
            <CardContent>
              <SimpleFolderSelector
                label="System Log Path"
                description="Select the directory where system logs will be stored."
                fetchCommand="get_system_log_path"
                saveCommand="set_system_log_path"
              />
            </CardContent>
          </Card>
        );
      case 'System-Docket': // Added Docket section content under System
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>Docket Settings</CardTitle>
              <CardDescription>Configure Docket features and behavior.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Docket Panel</h4>
                  <p className="text-sm text-muted-foreground">Settings for the Docket panel will appear here.</p>
                </div>
                {/* Placeholder for actual docket content */}
              </div>
            </CardContent>
          </Card>
        );
      case 'Addons-Plugins': // Changed from Plugins-Manage Plugins
        // Deduplicate plugins
        const uniquePlugins = [...new Map(plugins.map(p => [p.name, p])).values()];
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>Plugins</CardTitle>
              <CardDescription>Enable or disable installed plugins. Configure the path to your plugins directory.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-8">
              {/* Section 1: Search Bar */}
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">Search Plugins</h3>
                <Input
                  type="search"
                  placeholder="Search plugins..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>

              {/* Section 2: Path Configuration */}
              <div className="space-y-4">
                <SimpleFolderSelector
                  label="Plugin Directory"
                  description="Select the main directory where plugins are located. The application will scan this directory for valid plugins."
                  fetchCommand="get_plugins_path"
                  saveCommand="set_plugins_path"
                  onFolderSelected={handlePluginsPathSelected}
                />
              </div>

              {/* Section 3: Plugin Display */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Installed Plugins</h3>
                {loadingPlugins && (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">Loading plugins...</p>
                  </div>
                )}

                {pluginError && (
                  <div className="text-center py-8">
                    <p className="text-destructive">{pluginError}</p>
                  </div>
                )}

                {!loadingPlugins && !pluginError && uniquePlugins.length === 0 && (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">No plugins found or installed.</p>
                  </div>
                )}

                {!loadingPlugins && !pluginError && uniquePlugins.length > 0 && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {uniquePlugins
                      .filter(plugin =>
                        plugin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        (plugin.description && plugin.description.toLowerCase().includes(searchTerm.toLowerCase()))
                      )
                      .map((plugin) => (
                        <Card key={plugin.name} className="p-4">
                          <div className="flex flex-col space-y-3">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <h4 className="font-semibold text-base">{plugin.name}</h4>
                                <span className="text-xs text-muted-foreground">v{plugin.version}</span>
                              </div>
                              <Switch
                                id={`plugin-${plugin.name}`}
                                checked={plugin.enabled}
                                onCheckedChange={() => togglePlugin(plugin.name, plugin.enabled)}
                              />
                            </div>
                            <p className="text-sm text-muted-foreground line-clamp-2">
                              {plugin.description || 'No description available.'}
                            </p>
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`plugin-${plugin.name}`} className="text-sm font-medium">
                                {plugin.enabled ? 'Enabled' : 'Disabled'}
                              </Label>
                            </div>
                          </div>
                        </Card>
                      ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        );
      case 'System-Logic': // Content for Logic (formerly Rules) under System
        return (
          <div className="m-6 space-y-6">
            {/* File Path Selector at the top */}
            <Card>
              <CardHeader>
                <CardTitle>Logic System Configuration</CardTitle>
                <CardDescription>Configure the directory where AI logic components are stored. This includes system prompts, modals, and agents.</CardDescription>
              </CardHeader>
              <CardContent>
                <SimpleFolderSelector
                  label="Logic Directory"
                  description="Select the directory where system prompts, modals, and agents are stored. These files form the backbone of the AI system."
                  fetchCommand="get_system_prompts_path"
                  saveCommand="set_system_prompts_path"
                  onFolderSelected={handleSystemPromptsPathSelected}
                />
              </CardContent>
            </Card>

            {/* Use the new unified FileManager with Logic section configuration */}
            <FileManager 
              basePath={userSettings?.system_prompts_path}
              sections={[
                {
                  key: 'systemPrompts',
                  name: 'System Prompts',
                  icon: faFile,
                  color: 'blue',
                  description: 'Text-based prompts that define core AI behavior (Level 1 - Foundation)',
                  addLabel: 'System Prompt',
                  addDescription: 'Create new system prompt (.txt)',
                  folderNames: ['SystemPrompts', 'System Prompts'],
                  fileExtensions: ['.txt', '.md'],
                  cascadeLevel: 1,
                  hasMetadata: true
                },
                {
                  key: "modals",
                  name: "Modals",
                  icon: faBoxOpen,
                  color: "green",
                  description: "Modal configurations",
                  folderNames: ["Modals"],
                  fileExtensions: [".json"],
                  cascadeLevel: 2,
                  hasMetadata: true
                },
                {
                  key: "agents",
                  name: "Agents",
                  icon: faRobot,
                  color: "purple",
                  description: "AI agents",
                  folderNames: ["Agents"],
                  fileExtensions: [".json"],
                  cascadeLevel: 3,
                  hasMetadata: true
                }
              ]}
              onPathSelected={handleSystemPromptsPathSelected}
              title="Cascading Logic System"
              description="Manage AI logic in hierarchical layers: System Prompts → Modals → Agents"
              enableCascadeView={true}
            />
          </div>
        );
      case 'System-Extensions': // Content for Extensions under System
        return (
          <div className="m-6 space-y-6">
            {/* Extensions Header */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-3">
                  <FontAwesomeIcon icon={faPlug} className="h-6 w-6 text-blue-500" />
                  <span>Custom File Extensions</span>
                </CardTitle>
                <CardDescription>
                  Manage custom file types and their associated applications. These extensions enable specialized AI capabilities and modular system components.
                </CardDescription>
              </CardHeader>
            </Card>

            {/* Extensions Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* .prompt Extension */}
              <Card className="hover:shadow-lg transition-shadow cursor-pointer border-blue-200">
                <CardHeader className="text-center">
                  <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <FontAwesomeIcon icon={faFile} className="h-8 w-8 text-blue-600" />
                  </div>
                  <CardTitle className="text-lg">.prompt</CardTitle>
                  <CardDescription className="text-sm">
                    System prompts that define AI behavior and personality
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-xs text-muted-foreground">
                    <div className="flex justify-between">
                      <span>File Type:</span>
                      <span className="font-mono bg-blue-50 px-2 py-1 rounded">Text</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Handler:</span>
                      <span>Prompt Editor</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Status:</span>
                      <span className="text-green-600">✓ Active</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* .context Extension */}
              <Card className="hover:shadow-lg transition-shadow cursor-pointer border-green-200">
                <CardHeader className="text-center">
                  <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                    <FontAwesomeIcon icon={faDatabase} className="h-8 w-8 text-green-600" />
                  </div>
                  <CardTitle className="text-lg">.context</CardTitle>
                  <CardDescription className="text-sm">
                    Contextual information and knowledge base entries
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-xs text-muted-foreground">
                    <div className="flex justify-between">
                      <span>File Type:</span>
                      <span className="font-mono bg-green-50 px-2 py-1 rounded">JSON</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Handler:</span>
                      <span>Context Manager</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Status:</span>
                      <span className="text-green-600">✓ Active</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* .modal Extension */}
              <Card className="hover:shadow-lg transition-shadow cursor-pointer border-purple-200">
                <CardHeader className="text-center">
                  <div className="mx-auto w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                    <FontAwesomeIcon icon={faBoxOpen} className="h-8 w-8 text-purple-600" />
                  </div>
                  <CardTitle className="text-lg">.modal</CardTitle>
                  <CardDescription className="text-sm">
                    Specialized nano-algorithms for expertise domains
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-xs text-muted-foreground">
                    <div className="flex justify-between">
                      <span>File Type:</span>
                      <span className="font-mono bg-purple-50 px-2 py-1 rounded">Binary</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Handler:</span>
                      <span>Modal Engine</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Status:</span>
                      <span className="text-yellow-600">⚠ Development</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* .agent Extension */}
              <Card className="hover:shadow-lg transition-shadow cursor-pointer border-orange-200">
                <CardHeader className="text-center">
                  <div className="mx-auto w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mb-4">
                    <FontAwesomeIcon icon={faRobot} className="h-8 w-8 text-orange-600" />
                  </div>
                  <CardTitle className="text-lg">.agent</CardTitle>
                  <CardDescription className="text-sm">
                    AI agent profiles with task specializations
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-xs text-muted-foreground">
                    <div className="flex justify-between">
                      <span>File Type:</span>
                      <span className="font-mono bg-orange-50 px-2 py-1 rounded">JSON</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Handler:</span>
                      <span>Agent Manager</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Status:</span>
                      <span className="text-green-600">✓ Active</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Extension Management Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Extension Management</CardTitle>
                <CardDescription>
                  Configure file type associations and create new extensions
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-wrap gap-4">
                  <Button variant="outline" className="flex items-center space-x-2">
                    <FontAwesomeIcon icon={faPlus} className="h-4 w-4" />
                    <span>Add Extension</span>
                  </Button>
                  <Button variant="outline" className="flex items-center space-x-2">
                    <FontAwesomeIcon icon={faCog} className="h-4 w-4" />
                    <span>Configure Handlers</span>
                  </Button>
                  <Button variant="outline" className="flex items-center space-x-2">
                    <FontAwesomeIcon icon={faRefresh} className="h-4 w-4" />
                    <span>Refresh Registry</span>
                  </Button>
                </div>
                
                {/* Extension Statistics */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">3</div>
                    <div className="text-sm text-blue-600">Active Extensions</div>
                  </div>
                  <div className="text-center p-4 bg-yellow-50 rounded-lg">
                    <div className="text-2xl font-bold text-yellow-600">1</div>
                    <div className="text-sm text-yellow-600">In Development</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">42</div>
                    <div className="text-sm text-green-600">Files Registered</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">8</div>
                    <div className="text-sm text-purple-600">Handlers Available</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );
      case 'Addons-MCPs':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>MCPs (Modular Cognitive Processors)</CardTitle>
              <CardDescription>Configure the path to your MCPs directory.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <SimpleFolderSelector
                label="MCPs Directory"
                description="Select the main directory where MCPs are located."
                fetchCommand="get_mcps_path"
                saveCommand="set_mcps_path"
              />
              {/* Placeholder for MCPs list or management UI */}
              <p className="mt-4 text-muted-foreground">MCP management interface will be here.</p>
            </CardContent>
          </Card>
        );
      case 'Addons-APIs':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>APIs</CardTitle>
              <CardDescription>Configure the path to your API configurations directory or file.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <SimpleFolderSelector
                label="APIs Directory"
                description="Select the directory containing API configurations or a central API configuration file."
                fetchCommand="get_apis_path"
                saveCommand="set_apis_path"
              />
              {/* Placeholder for API keys management or list */}
              <p className="mt-4 text-muted-foreground">API configuration and key management interface will be here.</p>
            </CardContent>
          </Card>
        );
      // Add more cases for other settings pages as needed
      default:
        return (
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">{activeSidebarItem}</h2>
            <Card>
              <CardContent className="p-6">
                <p className="text-muted-foreground">
                  Settings for {activeSidebarItem} will be displayed here. This is a placeholder.
                </p>
              </CardContent>
            </Card>
          </div>
        );
    }
  };

  const handleChatSubmit = (message) => {
    // Handle chat submission in settings page
    console.log('Chat message from settings:', message);
    // You can implement chat functionality here
  };

  // Generate breadcrumb path
  const generateBreadcrumbPath = () => {
    const path = [
      { name: 'Home', icon: faHome, path: '/' },
      { name: 'Settings', icon: faCog, path: '/settings' }
    ];
    
    if (activeMainTab) {
      path.push({ name: activeMainTab, icon: mainTabsConfig.find(tab => tab.name === activeMainTab)?.icon || faCog, path: null });
    }
    
    if (activeSidebarItem) {
      path.push({ name: activeSidebarItem, icon: null, path: null });
    }
    
    return path;
  };

  const breadcrumbPath = generateBreadcrumbPath();

  return (
    <div className="flex flex-col h-screen bg-background text-foreground">
      {/* Enhanced Header with Breadcrumbs */}
      <header className="flex items-center justify-between p-4 border-b border-border bg-card text-card-foreground shadow-sm">
        <div className="flex items-center space-x-4">
          <Link to="/" className="flex items-center space-x-2 hover:text-primary transition-colors">
            <FontAwesomeIcon icon={faChevronLeft} className="h-5 w-5" />
            <span className="font-semibold text-lg">The Collective</span>
          </Link>
          <div className="hidden md:flex items-center space-x-2 text-muted-foreground">
            {breadcrumbPath.map((item, index) => (
              <React.Fragment key={item.name}>
                <FontAwesomeIcon icon={faChevronRight} className="h-3 w-3 mx-1" />
                {item.path ? (
                  <Link 
                    to={item.path} 
                    className="hover:text-foreground transition-colors flex items-center space-x-1"
                  >
                    {item.icon && <FontAwesomeIcon icon={item.icon} className="h-4 w-4" />}
                    <span>{item.name}</span>
                  </Link>
                ) : (
                  <div className="flex items-center space-x-1">
                    {item.icon && <FontAwesomeIcon icon={item.icon} className="h-4 w-4" />}
                    <span className="text-foreground">{item.name}</span>
                  </div>
                )}
              </React.Fragment>
            ))}
          </div>
        </div>
        <div className="text-sm text-muted-foreground">
          Settings
        </div>
      </header>

      {/* Modern Tab Navigation */}
      <Tabs value={activeMainTab} onValueChange={handleMainTabChange} className="border-b border-border">
        <TabsList className="flex justify-start px-4 pt-2 bg-card rounded-none w-full">
          {mainTabsConfig.map(tab => (
            <TabsTrigger 
              key={tab.name} 
              value={tab.name} 
              className="px-6 py-3 text-base data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:text-primary data-[state=active]:shadow-none rounded-none transition-all duration-200"
            >
              <div className="flex items-center space-x-2">
                <FontAwesomeIcon icon={tab.icon} className="h-5 w-5" />
                <span>{tab.name}</span>
              </div>
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>

      <div className="flex flex-1 overflow-hidden">
        {/* Enhanced Sidebar with Modern Design */}
        <aside className="w-72 border-r border-border bg-card p-4 space-y-4 flex flex-col">
          <div className="relative">
            <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search settings..."
              className="pl-10 bg-background focus-visible:ring-primary focus-visible:ring-offset-0 h-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          {/* Section Header */}
          <div className="px-2 py-1 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
            {activeMainTab} Settings
          </div>
          
          <nav className="flex-1 overflow-y-auto space-y-1 pr-1">
            {filteredSidebarItems.map(item => (
              <Button
                key={item.name}
                variant={activeSidebarItem === item.name ? "secondary" : "ghost"}
                className={`w-full justify-start text-base font-normal h-11 transition-all duration-200 ${
                  activeSidebarItem === item.name 
                    ? 'bg-primary/10 text-primary shadow-sm' 
                    : 'hover:bg-accent hover:text-accent-foreground'
                }`}
                onClick={() => setActiveSidebarItem(item.name)}
              >
                <FontAwesomeIcon icon={item.icon} className={`mr-3 h-5 w-5 ${activeSidebarItem === item.name ? 'text-primary' : 'text-muted-foreground'}`} />
                {item.name}
              </Button>
            ))}
            {filteredSidebarItems.length === 0 && searchTerm && (
                <p className='text-sm text-muted-foreground text-center py-4'>No settings found for "{searchTerm}".</p>
            )}
          </nav>
          
          {/* Sidebar Footer */}
          <div className="pt-4 border-t border-border">
            <div className="text-xs text-muted-foreground text-center">
              The Collective v0.1.0
            </div>
          </div>
        </aside>

        <main className="flex-1 overflow-y-auto bg-muted/20">
          <div className="">
            {renderContent()}
            {/* Extra scrolling space - 25% of viewport height */}
            {/* <div className="h-[25vh]"></div> */}
          </div>
        </main>
      </div>

      {/* Floating Chat System */}
      {/* <FloatingChat onSubmit={handleChatSubmit} isLoading={false} /> */}

      {/* VS Code Style Status Bar */}
      {/* <StatusBar /> */}
    </div>
  );
};

export default Settings;