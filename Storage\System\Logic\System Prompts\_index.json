{"version": "1.0", "lastUpdated": "2025-01-26", "description": "System prompts for AI behavior foundation", "cascadeLevel": 1, "items": [{"id": "general", "file": "general.txt", "name": "General Assistant", "description": "Base conversational AI behavior", "priority": 1, "category": "core", "triggers": ["*"], "active": true, "conditions": []}, {"id": "coding", "file": "coding.txt", "name": "Code Assistant", "description": "Enhanced coding and development assistance", "priority": 2, "category": "specialized", "triggers": ["code", "programming", "debug", "development"], "active": true, "conditions": ["file_type:code", "context:programming"]}, {"id": "creative", "file": "creative.txt", "name": "Creative Assistant", "description": "Creative writing and artistic assistance", "priority": 3, "category": "specialized", "triggers": ["creative", "writing", "story", "art"], "active": true, "conditions": ["context:creative", "task:writing"]}]}