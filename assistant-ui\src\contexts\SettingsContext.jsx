import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { safeInvoke } from '../utils/tauriHelpers';

// Default settings to use when backend is not available
const DEFAULT_SETTINGS = {
  dark_mode: window.matchMedia('(prefers-color-scheme: dark)').matches,
  theme: 'system',
  layout: 'default',
  plugin_states: {},
  system_log_path: './Storage/System/logs/',
  indexed_directory: './Storage/',
  plugins_path: './Storage/Addons/Plugins/',
  mcps_path: './Storage/Addons/MCP/',
  apis_path: './Storage/Addons/API/',
  models_path: './Storage/System/Models/',
  servers_path: './Storage/System/Servers/',
  system_prompts_path: './Storage/System/Logic/',
  date_format: 'YYYY-MM-DD',
  time_format: '24h',
  timezone: 'UTC',
  auto_save: true,
  startup_tab: 'chat',
  window_maximized: false,
  notifications_enabled: true,
  browser_homepage: 'https://www.google.com',
  browser_zoom_level: 100,
  browser_enable_javascript: true,
  browser_enable_images: true,
  browser_enable_cookies: true,
  browser_block_popups: true,
  server_url: 'http://127.0.0.1:11435',
  default_model: 'llama2',
  auto_start_server: false
};

const SettingsContext = createContext();

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (!context) {
    console.warn('useSettings must be used within a SettingsProvider. Using default settings.');
    return {
      userSettings: DEFAULT_SETTINGS,
      isLoading: false,
      error: null,
      isBackendAvailable: false,
      saveSettings: async () => {
        console.warn('Cannot save settings - SettingsProvider not found');
        return false;
      },
      saveUserSetting: async () => {
        console.warn('Cannot save setting - SettingsProvider not found');
        return false;
      },
      updateUserSettings: async () => {
        console.warn('Cannot update settings - SettingsProvider not found');
        return false;
      },
      refreshSettings: () => {}
    };
  }
  return context;
};

export const SettingsProvider = ({ children }) => {
  const [userSettings, setUserSettings] = useState(DEFAULT_SETTINGS);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isBackendAvailable, setIsBackendAvailable] = useState(true);

  // Apply dark mode whenever settings change
  useEffect(() => {
    if (userSettings?.dark_mode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [userSettings?.dark_mode]);

  const initializeSettings = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      console.log('Initializing settings...');

      // Try to load settings with a timeout
      const settings = await Promise.race([
        safeInvoke('load_user_settings').catch(e => {
          console.warn('Failed to load settings from backend, using defaults', e);
          return {}; // Return empty object to use defaults
        }),
        new Promise((resolve) => 
          setTimeout(() => {
            console.warn('Settings loading timeout, using defaults');
            resolve({}); // Resolve with empty object to use defaults
          }, 3000)
        )
      ]);
      
      console.log('Applying settings:', settings);
      
      // Always merge with defaults to ensure all settings exist
      const mergedSettings = {
        ...DEFAULT_SETTINGS,
        ...settings,
        // Ensure we always have a valid dark mode setting
        dark_mode: settings.dark_mode ?? 
                  (window.matchMedia('(prefers-color-scheme: dark)').matches)
      };
      
      setUserSettings(mergedSettings);
      setIsBackendAvailable(true);
    } catch (err) {
      console.error('Error loading settings:', err);
      setError(err.message || 'Failed to load settings');
      setIsBackendAvailable(false);
      
      // Use default settings but don't show error to user
      setUserSettings(DEFAULT_SETTINGS);
    } finally {
      setIsLoading(false);
      console.log('Settings initialization complete. Final isLoading state:', false);
      console.log('Final userSettings dark_mode:', userSettings?.dark_mode);
    }
  }, [isBackendAvailable]);

  // Load settings on mount and when backend availability changes
  useEffect(() => {
    initializeSettings();
  }, [initializeSettings]);

  // Save settings to backend
  const saveSettings = async (newSettings) => {
    try {
      // Try to save to backend, but don't fail if it doesn't work
      try {
        await safeInvoke('save_user_settings', { settings: newSettings });
      } catch (err) {
        console.warn('Could not save settings to backend, using local state only', err);
      }
      
      // Always update local state
      setUserSettings(prev => ({
        ...prev,
        ...newSettings
      }));
      return true;
    } catch (err) {
      console.error('Error in saveSettings:', err);
      setError('Failed to save settings. Changes may not persist after refresh.');
      return false;
    }
  };

  // Save a single setting
  const saveUserSetting = async (key, value) => {
    if (!userSettings) return false;

    try {
      const newSettings = { ...userSettings, [key]: value };
      
      // Try to save to backend, but don't fail if it doesn't work
      try {
        await safeInvoke('save_user_settings', { settings: newSettings });
      } catch (err) {
        console.warn(`Could not save setting '${key}' to backend, using local state only`, err);
      }
      
      // Always update local state
      setUserSettings(newSettings);
      console.log(`Applied setting ${key}:`, value);
      return true;
    } catch (err) {
      console.error('Error in saveUserSetting:', err);
      setError('Failed to save setting. Changes may not persist after refresh.');
      return false;
    }
  };

  // Update multiple settings at once
  const updateUserSettings = async (newSettings) => {
    if (!userSettings) return false;

    try {
      const updatedSettings = { ...userSettings, ...newSettings };
      await invoke('save_user_settings', { settings: updatedSettings });
      setUserSettings(updatedSettings);
      console.log('Updated user settings:', updatedSettings);
      return true;
    } catch (err) {
      console.error('Error updating user settings:', err);
      setError(err.message || 'Failed to update settings');
      return false;
    }
  };

  // Refresh settings from backend
  const refreshSettings = async () => {
    await initializeSettings();
  };

  // Provide the context value
  const value = {
    userSettings,
    isLoading,
    error,
    isBackendAvailable,
    saveSettings,
    saveUserSetting,
    updateUserSettings,
    refreshSettings
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
};
