// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use log::{info, error};

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn main() {
    // Initialize logger first
    env_logger::init();
    
    info!("Starting The Collective in minimal test mode...");
    
    // Use default configuration with all subsystems enabled
    let config = __ci_lib::InitializationConfig::default();
    
    if let Err(e) = __ci_lib::run_with_config(config) {
        error!("Application error: {}", e);
        std::process::exit(1);
    }
}
