import { useRef, useEffect, useCallback } from 'react';

/**
 * Enhanced custom hook to check if a component is mounted.
 * Prevents state updates on unmounted components which can cause:
 * "NotFoundError: Failed to execute 'removeChild' on 'Node'"
 *
 * Usage:
 * const isMounted = useIsMounted();
 *
 * useEffect(() => {
 *   if (isMounted()) {
 *     // Safe to update state
 *     setState(value);
 *   }
 * }, [isMounted]);
 */
export const useIsMounted = () => {
  const isMountedRef = useRef(false);

  useEffect(() => {
    isMountedRef.current = true;

    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Use useCallback to ensure stable reference
  return useCallback(() => isMountedRef.current, []);
};

/**
 * Safe state setter that only updates if component is mounted
 * Usage: const safeSetState = useSafeState(setState, isMounted);
 */
export const useSafeState = (setState, isMounted) => {
  return useCallback((value) => {
    if (isMounted()) {
      try {
        setState(value);
      } catch (error) {
        console.warn('Safe state update failed:', error);
      }
    }
  }, [setState, isMounted]);
};