import { useRef, useEffect } from 'react';

/**
 * Custom hook to check if a component is mounted.
 * Prevents state updates on unmounted components which can cause:
 * "NotFoundError: Failed to execute 'removeChild' on 'Node'"
 * 
 * Usage:
 * const isMounted = useIsMounted();
 * 
 * useEffect(() => {
 *   if (isMounted()) {
 *     // Safe to update state
 *     setState(value);
 *   }
 * }, [isMounted]);
 */
export const useIsMounted = () => {
  const isMountedRef = useRef(true);
  
  useEffect(() => {
    isMountedRef.current = true;
    
    return () => {
      isMountedRef.current = false;
    };
  }, []);
  
  return () => isMountedRef.current;
};