{"name": "Cha<PERSON>", "description": "AI-powered chat interface with real-time streaming, model management, and superior user experience.", "version": "1.0.0", "app_type": "core-tool", "enabled": true, "main": "chat.jsx", "class_name": "Cha<PERSON>", "plugin_type": "ui_component", "author": "The Collective Team", "permissions": ["ai_client", "database", "local_storage", "streaming"], "data_directory": "./data/", "versions": [{"version": "1.0.0", "path": "./versions/v1.0.0/", "changelog": "ChatFixed-based implementation with real-time streaming and superior UX"}], "features": ["Real-time streaming responses", "Model selection and preloading", "Typing indicators", "Welcome message generation", "Error handling and recovery", "Smooth scrolling and animations", "Professional UI design"], "settings": {"auto_welcome": true, "enable_streaming": true, "show_model_info": true, "auto_scroll": true, "typing_indicators": true}, "state_management": {"persistent": false, "auto_restore": false, "backup_enabled": false}}