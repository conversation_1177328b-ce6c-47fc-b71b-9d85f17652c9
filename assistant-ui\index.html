<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <meta
      name="description"
      content="The Collective - PC-based AI Assistant"
    />
    <link rel="apple-touch-icon" href="/logo192.png" />
    <link rel="manifest" href="/manifest.json" />
    <title>The Collective</title>
    <style>
      .loading-fallback {
        padding: 20px;
        font-family: Arial, sans-serif;
        text-align: center;
        color: #333;
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root">
      <div class="loading-fallback">
        <h2>Loading...</h2>
        <p>If this message persists, there may be a JavaScript loading issue.</p>
      </div>
    </div>
    <script>
      console.log('HTML script tag executed');
      window.addEventListener('error', function(e) {
        console.error('Global error:', e.error);
        document.getElementById('root').innerHTML = '<div style="padding: 20px; color: red;"><h2>JavaScript Error</h2><p>' + e.error + '</p></div>';
      });

      // Test if modules are supported
      if ('noModule' in HTMLScriptElement.prototype) {
        console.log('ES modules are supported');
      } else {
        console.log('ES modules are NOT supported');
        document.getElementById('root').innerHTML = '<div style="padding: 20px; color: red;"><h2>Browser Compatibility Issue</h2><p>Your browser does not support ES modules</p></div>';
      }
    </script>
    <script type="module" src="/src/index.jsx"></script>
  </body>
</html>