import React from 'react';
import <PERSON>rrorPopup from './ErrorPopup';

/**
 * Error Boundary component to catch and handle JavaScript errors in the component tree.
 * Prevents the entire app from crashing when a component throws an error.
 */
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to an error reporting service
    console.error('ErrorBoundary caught an error:', error, errorInfo);

    // Check if this is a DOM manipulation error and handle it gracefully
    const errorMessage = error?.message || '';
    if (errorMessage.includes('removeChild') ||
        errorMessage.includes('insertBefore') ||
        errorMessage.includes('appendChild') ||
        error?.name === 'NotFoundError') {
      console.warn('DOM manipulation error caught by <PERSON>rrorBoundary, attempting recovery...');

      // Attempt to recover by resetting the error state after a short delay
      setTimeout(() => {
        if (this.state.hasError) {
          this.setState({
            hasError: false,
            error: null,
            errorInfo: null
          });
        }
      }, 1000);
    }

    // Update state with error details
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
  }

  handleReload = () => {
    // Reset error state
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null
    });
  };

  render() {
    if (this.state.hasError) {
      // Render fallback UI using the new ErrorPopup component
      return (
        <ErrorPopup
          title="Something went wrong"
          message="We're sorry, but an error occurred in the application. Please try reloading the page."
          error={this.state.error}
          onReload={() => window.location.reload()}
          onRetry={this.handleReload}
        />
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;