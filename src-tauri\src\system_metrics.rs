use serde::Serialize;
use sysinfo::System;

#[derive(Serialize)]
pub struct SystemMetrics {
    cpu_usage: f32,
    memory_usage: MemoryUsage,
    total_memory: f64,
    used_memory: f64,
    free_memory: f64,
    used_gb: f64,
    total_gb: f64,
    free_gb: f64,
}

#[derive(Serialize)]
pub struct MemoryUsage {
    used: u64,
    total: u64,
    free: u64,
    used_gb: f64,
    total_gb: f64,
    free_gb: f64,
}

#[tauri::command]
pub fn get_system_metrics() -> Result<SystemMetrics, String> {
    let mut sys = System::new_all();
    
    // Refresh system information
    sys.refresh_all();
    
    // Get CPU usage (global CPU usage)
    sys.refresh_cpu();
    let cpu_usage = sys.global_cpu_info().cpu_usage();
    
    // Get memory information
    let total_memory = sys.total_memory() as f64 / (1024.0 * 1024.0 * 1024.0); // Convert to GB
    let used_memory = sys.used_memory() as f64 / (1024.0 * 1024.0 * 1024.0); // Convert to GB
    let free_memory = sys.free_memory() as f64 / (1024.0 * 1024.0 * 1024.0); // Convert to GB
    
    let memory_usage = MemoryUsage {
        used: sys.used_memory(),
        total: sys.total_memory(),
        free: sys.free_memory(),
        used_gb: used_memory,
        total_gb: total_memory,
        free_gb: free_memory,
    };
    
    Ok(SystemMetrics {
        cpu_usage: cpu_usage as f32,
        memory_usage,
        total_memory,
        used_memory,
        free_memory,
        used_gb: used_memory,
        total_gb: total_memory,
        free_gb: free_memory,
    })
}
