{"version": "1.0", "lastUpdated": "2025-01-26", "description": "Modal configurations for context modification", "cascadeLevel": 2, "dependsOn": ["systemPrompts"], "items": [{"id": "debug", "file": "debug.json", "name": "Debug Mode", "description": "Enhanced debugging context and verbosity", "category": "development", "triggers": ["debug", "error", "troubleshoot"], "active": true, "appliesAfter": ["coding"], "conditions": ["user_intent:debug", "context:error"]}, {"id": "explain", "file": "explain.json", "name": "Detailed Explanation", "description": "Provides step-by-step explanations with examples", "category": "educational", "triggers": ["explain", "how", "why", "teach"], "active": true, "appliesAfter": ["general", "coding"], "conditions": ["user_intent:learning", "complexity:high"]}, {"id": "concise", "file": "concise.json", "name": "Concise Mode", "description": "Brief, direct responses without elaboration", "category": "efficiency", "triggers": ["brief", "quick", "summary"], "active": true, "appliesAfter": ["*"], "conditions": ["user_preference:concise", "time_constraint:true"]}]}