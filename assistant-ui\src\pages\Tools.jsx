import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { open } from '@tauri-apps/plugin-shell';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "../components/ui/card.jsx";
import { Button } from '../components/ui/button.jsx';
import { Input } from '../components/ui/input.jsx';
import { Switch } from '../components/ui/switch.jsx';
import { Label } from '../components/ui/label.jsx';
import { Badge } from '../components/ui/badge.jsx';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select.jsx';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlug, faSearch, faTools, faGlobe, faCog, faDesktop, faGem, faUser, faComments, faFolder } from '@fortawesome/free-solid-svg-icons';
import './Tools.css';

const Tools = ({ addTab }) => {
  const [plugins, setPlugins] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedVersions, setSelectedVersions] = useState({});

  const togglePlugin = async (pluginName, currentStatus) => {
    try {
      const newStatus = !currentStatus;
      await invoke('toggle_plugin', { plugin_name: pluginName, enabled: newStatus });
      // Update local state to reflect the change
      setPlugins(plugins.map(p => p.name === pluginName ? { ...p, enabled: newStatus } : p));
    } catch (err) {
      console.error(`Error toggling plugin ${pluginName}:`, err);
      setError(`Failed to update plugin status for ${pluginName}.`);
    }
  };

  const handleVersionChange = async (pluginName, version) => {
    try {
      setSelectedVersions(prev => ({ ...prev, [pluginName]: version }));
      // Save the selected version to plugin settings
      await invoke('save_plugin_data', {
        plugin_name: pluginName,
        data_key: 'active_version',
        data_value: version
      });
    } catch (err) {
      console.error(`Error changing version for ${pluginName}:`, err);
      setError(`Failed to change version for ${pluginName}.`);
    }
  };

  useEffect(() => {
    const fetchPlugins = async () => {
      try {
        setLoading(true);
        const pluginsList = await invoke('get_plugins');
        setPlugins(pluginsList);
        
        // Load selected versions for each plugin
        const versions = {};
        for (const plugin of pluginsList) {
          try {
            const activeVersion = await invoke('get_plugin_data', {
              plugin_name: plugin.name,
              data_key: 'active_version'
            });
            versions[plugin.name] = activeVersion || plugin.version;
          } catch {
            // Use default version if no active version is saved
            versions[plugin.name] = plugin.version;
          }
        }
        setSelectedVersions(versions);
        setError(null);
      } catch (err) {
        console.error('Error fetching plugins:', err);
        setError('Failed to load tools. Make sure the plugins path is configured and contains valid plugins.');
      } finally {
        setLoading(false);
      }
    };

    fetchPlugins();
  }, []);

  // Categorize plugins
  const categorizePlugins = () => {
    const categories = {
      'core-tool': [],
      'web-app': [],
      'app': []
    };
    
    plugins.forEach(plugin => {
      const category = plugin.app_type || 'app';
      if (categories[category]) {
        categories[category].push(plugin);
      } else {
        categories['app'].push(plugin);
      }
    });
    
    return categories;
  };

  const categorizedPlugins = categorizePlugins();
  
  const filteredPlugins = plugins.filter(plugin => {
    const matchesSearch = plugin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (plugin.description && plugin.description.toLowerCase().includes(searchTerm.toLowerCase()));
    
    if (selectedCategory === 'all') return matchesSearch;
    return matchesSearch && (plugin.app_type || 'app') === selectedCategory;
  });

  const getCategoryIcon = (category) => {
    switch(category) {
      case 'core-tool': return faGem;
      case 'web-app': return faGlobe;
      case 'app': return faPlug;
      default: return faPlug;
    }
  };

  const getCategoryLabel = (category) => {
    switch(category) {
      case 'core-tool': return 'CORE TOOL';
      case 'web-app': return 'WEB APP';
      case 'app': return 'APP';
      default: return 'APP';
    }
  };

  const getCategoryColor = (category) => {
    switch(category) {
      case 'core-tool': return 'bg-gradient-to-r from-purple-500 to-purple-700';
      case 'web-app': return 'bg-gradient-to-r from-blue-500 to-blue-700';
      case 'app': return 'bg-gradient-to-r from-green-500 to-green-700';
      default: return 'bg-gradient-to-r from-gray-500 to-gray-700';
    }
  };

  const renderAppCard = (plugin) => (
    <Card key={plugin.name} className="hover:shadow-lg transition-shadow duration-200">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg">{plugin.name}</CardTitle>
            <div className="flex items-center gap-2 mt-1">
              <Badge 
                className={`text-white text-xs ${getCategoryColor(plugin.app_type || 'app')}`}
              >
                {getCategoryLabel(plugin.app_type || 'app')}
              </Badge>
              <span className="text-xs text-muted-foreground">v{selectedVersions[plugin.name] || plugin.version}</span>
              {plugin.author && (
                <span className="text-xs text-muted-foreground">by {plugin.author}</span>
              )}
            </div>
          </div>
          <Switch
            id={`plugin-${plugin.name}`}
            checked={plugin.enabled}
            onCheckedChange={() => togglePlugin(plugin.name, plugin.enabled)}
          />
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <CardDescription className="text-sm mb-3 line-clamp-2">
          {plugin.description || 'No description available.'}
        </CardDescription>
        
        {plugin.features && plugin.features.length > 0 && (
          <div className="mb-3">
            <p className="text-xs font-medium text-muted-foreground mb-2">Features:</p>
            <div className="flex flex-wrap gap-1">
              {plugin.features.slice(0, 3).map((feature, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {feature}
                </Badge>
              ))}
              {plugin.features.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{plugin.features.length - 3} more
                </Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>
      
      <CardFooter className="pt-0">
        <div className="flex flex-col gap-3 w-full">
          {/* Version Selector */}
          {plugin.versions && plugin.versions.length > 1 && (
            <div className="flex items-center gap-2">
              <Label className="text-xs font-medium">Version:</Label>
              <Select
                value={selectedVersions[plugin.name] || plugin.version}
                onValueChange={(value) => handleVersionChange(plugin.name, value)}
              >
                <SelectTrigger className="h-6 text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {plugin.versions.map((v, index) => (
                    <SelectItem key={index} value={v.version} className="text-xs">
                      v{v.version}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          
          {/* Enable/Disable and Open controls */}
          <div className="flex items-center justify-between">
            <Label htmlFor={`plugin-${plugin.name}`} className="text-sm font-medium">
              {plugin.enabled ? '● Enabled' : '○ Disabled'}
            </Label>
            <div className="flex items-center gap-2">
              <Switch
                id={`plugin-${plugin.name}`}
                checked={plugin.enabled}
                onCheckedChange={() => togglePlugin(plugin.name, plugin.enabled)}
                className="scale-75"
              />
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  if (plugin.name === 'Browser') {
                    addTab({ id: 'browser', name: 'Browser', icon: faGlobe });
                  } else if (plugin.name === 'Chat') {
                    addTab({ id: 'chat-assistant', name: 'Chat', icon: faComments });
                  } else if (plugin.name === 'File Explorer') {
                    addTab({ id: 'file-explorer', name: 'File Explorer', icon: faFolder });
                  }
                }}
              >
                Open
              </Button>
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );

  const renderCategorySection = (category, plugins) => {
    if (plugins.length === 0) return null;
    
    return (
      <div key={category} className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <div className={`p-2 rounded-lg ${getCategoryColor(category)}`}>
            <FontAwesomeIcon icon={getCategoryIcon(category)} className="h-5 w-5 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-100">
              {getCategoryLabel(category)}S
            </h2>
            <p className="text-sm text-slate-600 dark:text-slate-400">
              {plugins.length} {plugins.length === 1 ? 'app' : 'apps'} available
            </p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {plugins.map(renderAppCard)}
        </div>
      </div>
    );
  };

  return (
    <div className="tools-container p-8 bg-slate-50 dark:bg-slate-900 min-h-screen">
      {/* Enhanced Header */}
      <div className="mb-8 text-center">
        <div className="inline-block p-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-4 shadow-lg">
          <FontAwesomeIcon icon={faTools} className="h-8 w-8 text-white" />
        </div>
        <h1 className="text-4xl font-extrabold text-slate-800 dark:text-slate-100 tracking-tight">
          App Store
        </h1>
        <p className="mt-3 text-lg text-slate-600 dark:text-slate-400 max-w-2xl mx-auto">
          Discover and manage your modular applications and tools.
        </p>
      </div>

      {/* Search and Filter Bar */}
      <div className="mb-8 max-w-4xl mx-auto">
        <div className="flex gap-4 items-center">
          <div className="relative flex-1">
            <FontAwesomeIcon icon={faSearch} className="absolute left-4 top-1/2 -translate-y-1/2 text-slate-400" />
            <Input
              type="text"
              placeholder="Search apps and tools..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-12 pr-4 py-3 bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-700 rounded-full shadow-sm focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="flex gap-2">
            <Button
              variant={selectedCategory === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory('all')}
            >
              All
            </Button>
            <Button
              variant={selectedCategory === 'core-tool' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory('core-tool')}
            >
              Core Tools
            </Button>
            <Button
              variant={selectedCategory === 'web-app' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory('web-app')}
            >
              Web Apps
            </Button>
            <Button
              variant={selectedCategory === 'app' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory('app')}
            >
              Apps
            </Button>
          </div>
        </div>
      </div>

      {loading && (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-slate-600 dark:text-slate-400">Loading apps...</p>
        </div>
      )}
      
      {error && (
        <div className="text-center py-8">
          <p className="text-red-500 bg-red-100 dark:bg-red-900/30 p-4 rounded-lg max-w-2xl mx-auto">
            Error: {error}
          </p>
        </div>
      )}

      {!loading && !error && (
        <div>
          {/* Show filtered results if searching */}
          {searchTerm || selectedCategory !== 'all' ? (
            <div>
              <div className="mb-4">
                <p className="text-slate-600 dark:text-slate-400">
                  {filteredPlugins.length} {filteredPlugins.length === 1 ? 'app' : 'apps'} found
                  {searchTerm && ` for "${searchTerm}"`}
                  {selectedCategory !== 'all' && ` in ${getCategoryLabel(selectedCategory)}S`}
                </p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredPlugins.map(renderAppCard)}
              </div>
            </div>
          ) : (
            /* Show categorized view */
            <div>
              {renderCategorySection('core-tool', categorizedPlugins['core-tool'])}
              {renderCategorySection('web-app', categorizedPlugins['web-app'])}
              {renderCategorySection('app', categorizedPlugins['app'])}
            </div>
          )}
          
          {!loading && !error && filteredPlugins.length === 0 && (
            <div className="text-center py-12">
              <FontAwesomeIcon icon={faSearch} className="h-16 w-16 text-slate-400 mb-4" />
              <p className="text-slate-600 dark:text-slate-400 text-lg">
                No apps found {searchTerm ? `for "${searchTerm}"` : ''}
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Tools;
