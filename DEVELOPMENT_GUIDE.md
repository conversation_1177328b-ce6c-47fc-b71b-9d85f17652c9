# Development Guide

This guide provides instructions on how to set up and run the development environment for this project. Following these guidelines will help prevent common issues related to project structure and script execution.

## Project Structure

The project is organized into a monorepo-like structure with a root directory and several key sub-directories:

- **`e:\TheCollective\` (Root Directory):** This is the main entry point for the project. It contains the primary `package.json` and configuration files for managing the entire application.

- **`e:\TheCollective\assistant-ui\`:** This directory contains the React-based frontend application.

- **`e:\TheCollective\src-tauri\`:** This directory holds the Rust-based Tauri backend.

## Key Configuration Files

- **`e:\TheCollective\package.json`:** The main package file for the entire project. It contains the scripts for running the application in development and building it for production.

- **`e:\TheCollective\assistant-ui\package.json`:** The package file for the frontend application. It contains the dependencies and scripts specific to the React app.

- **`e:\TheCollective\vite.config.ts`:** The Vite configuration for the root project.

- **`e:\TheCollective\assistant-ui\tauri.conf.json`:** The Tauri configuration file, which defines how the frontend and backend are bundled together.

## Running the Development Server

To run the application in development mode, you must execute the `dev` script from the **root directory** of the project.

```bash
# Navigate to the root directory
cd e:\TheCollective

# Run the development server
npm run dev
```

This command uses `concurrently` to start both the frontend development server and the Tauri backend simultaneously.

### Common Pitfalls

- **Do not run `npm` commands from within the `assistant-ui` directory.** All development activities should be initiated from the root of the project to ensure that both the frontend and backend are launched correctly.