import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App.jsx';
import TestApp from './TestApp.jsx';
import reportWebVitals from './reportWebVitals';

console.log('index.jsx loaded');

// Global error handler for DOM manipulation errors
window.addEventListener('error', (event) => {
  if (event.error && event.error.message && event.error.message.includes('removeChild')) {
    console.warn('DOM removeChild error caught and suppressed:', event.error);
    event.preventDefault(); // Prevent the error from crashing the app
    return false;
  }
});

// Global unhandled promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
  if (event.reason && event.reason.message && event.reason.message.includes('removeChild')) {
    console.warn('DOM removeChild promise rejection caught and suppressed:', event.reason);
    event.preventDefault();
    return false;
  }
});

try {
  const root = ReactDOM.createRoot(document.getElementById('root'));
  console.log('React root created');

  root.render(
    <App />
  );
  console.log('App rendered');
} catch (error) {
  console.error('Error in index.jsx:', error);
  document.getElementById('root').innerHTML = '<div class="loading-fallback"><h2>React Error</h2><p>' + error.message + '</p></div>';
}

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();