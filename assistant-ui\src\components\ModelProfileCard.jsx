import React, { useState, useMemo } from 'react';
import { 
  Database as DatabaseIcon, 
  Play as PlayIcon, 
  Download as DownloadIcon, 
  Star as StarIcon,
  HardDrive as HardDriveIcon,
  RefreshCw as RefreshCwIcon,
  Trash2 as TrashIcon,
  File as FileIcon,
  Folder as FolderIcon,
  CheckCircle as CheckCircleIcon,
  AlertTriangle as AlertTriangleIcon,
  Eye as EyeIcon,
  Settings as SettingsIcon
} from 'lucide-react';

const ModelProfileCard = ({ 
  profile, 
  isActive = false, 
  modelInfo = null,
  onToggle,
  onSetActive,
  onDelete,
  onDownload,
  className = ''
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Enhanced model icon with status indicator
  const getModelIcon = useMemo(() => {
    return (
      <div className="relative">
        <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg text-white text-sm flex items-center justify-center font-bold">
          AI
        </div>
        {isActive && (
          <StarIcon className="absolute -top-1 -right-1 w-4 h-4 text-yellow-500 fill-current" />
        )}
      </div>
    );
  }, [isActive]);

  const formatFileSize = (bytes) => {
    if (!bytes) return 'Unknown size';
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${Math.round(bytes / Math.pow(1024, i) * 100) / 100} ${sizes[i]}`;
  };

  // Enhanced model status with animations
  const getModelStatus = useMemo(() => {
    if (modelInfo?.exists) {
      return {
        status: 'available',
        color: 'text-green-500',
        bgColor: 'bg-green-500',
        icon: CheckCircleIcon,
        text: 'Available',
        pulse: false
      };
    }
    return {
      status: 'not_found',
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-500',
      icon: AlertTriangleIcon,
      text: 'Not Found',
      pulse: true
    };
  }, [modelInfo?.exists]);

  const StatusIcon = getModelStatus.icon;

  const handleDownload = async () => {
    if (onDownload) {
      setIsLoading(true);
      try {
        await onDownload(profile.name);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const status = getModelStatus;

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-5 transition-all duration-300 hover:shadow-lg ${className}`}>
      {/* Header with enhanced status indicators */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          {getModelIcon}
          <div>
            <div className="flex items-center space-x-2">
              <h3 className="font-semibold text-gray-900 dark:text-white text-base">{profile.name}</h3>
              {isActive && (
                <StarIcon className="w-4 h-4 text-yellow-500 fill-current animate-pulse" title="Active Model" />
              )}
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
              <StatusIcon className={`w-4 h-4 ${status.color}`} />
              <span>{status.text}</span>
              {modelInfo?.size && (
                <>
                  <span>•</span>
                  <span>{formatFileSize(modelInfo.size)}</span>
                </>
              )}
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Quick Actions */}
          <div className="flex items-center space-x-1">
            {/* Set Active Button */}
            {profile.enabled && status.status === 'available' && (
              <button
                onClick={() => onSetActive && onSetActive(profile.name)}
                className={`px-3 py-1 rounded-full text-xs font-medium transition-all duration-200 transform hover:scale-105 ${
                  isActive 
                    ? 'bg-gradient-to-r from-yellow-400 to-yellow-500 text-white shadow-md'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                }`}
                title={isActive ? 'Currently Active' : 'Set as Active'}
              >
                {isActive ? 'Active' : 'Set Active'}
              </button>
            )}

            {/* Download Button */}
            {status.status === 'not_found' && (
              <button
                onClick={handleDownload}
                disabled={isLoading}
                className="flex items-center space-x-1 px-3 py-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 text-xs transition-all duration-200 transform hover:scale-105 shadow-md"
                title="Download Model"
              >
                <DownloadIcon className={`w-3 h-3 ${isLoading ? 'animate-spin' : ''}`} />
                <span>{isLoading ? 'Downloading...' : 'Download'}</span>
              </button>
            )}

            {/* View Details Button */}
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors duration-200"
              title={showDetails ? 'Hide Details' : 'View Details'}
            >
              <EyeIcon className="w-4 h-4" />
            </button>
          </div>

          {/* Enable/Disable Toggle with enhanced styling */}
          <label className="flex items-center cursor-pointer group">
            <input
              type="checkbox"
              checked={profile.enabled}
              onChange={(e) => onToggle && onToggle(profile.name, e.target.checked)}
              className="sr-only"
            />
            <div className={`relative w-10 h-5 rounded-full transition-colors duration-300 ease-in-out ${
              profile.enabled ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'
            }`}>
              <div className={`absolute top-0.5 left-0.5 w-4 h-4 bg-white rounded-full transition-all duration-300 ease-in-out transform ${
                profile.enabled ? 'translate-x-5' : ''
              } shadow-md`} />
            </div>
          </label>
        </div>
      </div>

      {/* Model Path with enhanced styling */}
      {modelInfo && (
        <div className="mb-4">
          <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Model Path</div>
          <div className="text-sm font-mono bg-gray-50 dark:bg-gray-700 p-3 rounded-lg border text-gray-700 dark:text-gray-300 truncate transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-600">
            {modelInfo.path || 'Not found'}
          </div>
        </div>
      )}

      {/* Quick Stats with enhanced layout */}
      {modelInfo && (
        <div className="grid grid-cols-2 gap-4 text-sm mb-4">
          <div className="flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <HardDriveIcon className="w-4 h-4 text-gray-500" />
            <div>
              <div className="text-gray-600 dark:text-gray-400 text-xs">Size</div>
              <div className="font-medium text-gray-900 dark:text-white">
                {modelInfo.size ? formatFileSize(modelInfo.size) : 'Unknown'}
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <FileIcon className="w-4 h-4 text-gray-500" />
            <div>
              <div className="text-gray-600 dark:text-gray-400 text-xs">Type</div>
              <div className="font-medium text-gray-900 dark:text-white">
                {modelInfo.type || 'GGUF'}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Detailed View with smooth transition */}
      {showDetails && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 animate-fadeIn">
          {/* Model Metadata */}
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Model Information</h4>
              <div className="grid grid-cols-1 gap-2 text-sm">
                <div className="flex justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                  <span className="text-gray-500 dark:text-gray-400">Name:</span>
                  <span className="text-gray-900 dark:text-white font-medium">{profile.name}</span>
                </div>
                <div className="flex justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                  <span className="text-gray-500 dark:text-gray-400">Status:</span>
                  <div className="flex items-center space-x-1">
                    <span className={status.color}>{status.icon}</span>
                    <span className="text-gray-900 dark:text-white">{status.text}</span>
                  </div>
                </div>
                <div className="flex justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                  <span className="text-gray-500 dark:text-gray-400">Enabled:</span>
                  <span className={`font-medium ${ 
                    profile.enabled ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                  }`}>
                    {profile.enabled ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
            </div>

            {/* File Information */}
            {modelInfo && modelInfo.exists && (
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">File Details</h4>
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">Full Path:</span>
                      <div className="font-mono text-xs bg-white dark:bg-gray-800 p-2 rounded border mt-1 break-all">
                        {modelInfo.fullPath}
                      </div>
                    </div>
                    {modelInfo.lastModified && (
                      <div className="flex justify-between p-2 bg-white dark:bg-gray-800 rounded">
                        <span className="text-gray-500 dark:text-gray-400">Last Modified:</span>
                        <span className="text-gray-900 dark:text-white">
                          {new Date(modelInfo.lastModified).toLocaleString()}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Usage Instructions */}
            {profile.enabled && status.status === 'available' && (
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Usage</h4>
                <div className="text-sm text-gray-600 dark:text-gray-400 bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                  <p>This model is ready to use. {isActive ? 'It is currently the active model.' : 'Click "Set Active" to use it for new conversations.'}</p>
                </div>
              </div>
            )}

            {/* Delete Action */}
            <div className="flex justify-end pt-2">
              <button
                onClick={() => onDelete && onDelete(profile.name)}
                className="flex items-center space-x-1 px-3 py-2 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg hover:from-red-600 hover:to-red-700 text-sm transition-all duration-200 transform hover:scale-105 shadow-md"
                title="Delete Model Profile"
              >
                <TrashIcon className="w-4 h-4" />
                <span>Delete</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ModelProfileCard;