use rusqlite::{Connection, Result};
use std::path::Path;
use std::fs;
use crate::settings_manager;
use log::{error, info};

pub fn establish_connection() -> Result<Connection, rusqlite::Error> {
    // Ensure storage directory exists with better error handling
    settings_manager::ensure_directories().map_err(|e| {
        let error_msg = format!("Failed to create storage directories: {}", e);
        error!("{}", error_msg);
        rusqlite::Error::SqliteFailure(
            rusqlite::ffi::Error::new(1), // SQLITE_ERROR
            Some(error_msg.into())
        )
    })?;
    
    // Use the correct storage path from settings_manager
    let db_path = settings_manager::STORAGE_DIR.join("storage.db");
    let db_path_str = db_path.to_str().ok_or_else(|| {
        let error_msg = format!("Invalid database path: {:?}", db_path);
        error!("{}", error_msg);
        rusqlite::Error::SqliteFailure(
            rusqlite::ffi::Error::new(1), // SQLITE_ERROR
            Some(error_msg.into())
        )
    })?;

    info!("Connecting to database at: {}", db_path_str);

    // Ensure the directory exists with better error handling
    if let Some(parent) = db_path.parent() {
        info!("Ensuring parent directory exists: {}", parent.display());
        if let Err(e) = std::fs::create_dir_all(parent) {
            let error_msg = format!("Failed to create database directory: {}", e);
            error!("{}", error_msg);
            return Err(rusqlite::Error::SqliteFailure(
                rusqlite::ffi::Error::new(1), // SQLITE_ERROR
                Some(error_msg.into())
            ));
        }
    } else {
        let error_msg = format!("Could not determine parent directory for: {}", db_path_str);
        error!("{}", error_msg);
        return Err(rusqlite::Error::SqliteFailure(
            rusqlite::ffi::Error::new(1), // SQLITE_ERROR
            Some(error_msg.into())
        ));
    }

    // Try to open the database with a timeout
    let connection_result = std::panic::catch_unwind(|| {
        Connection::open(&db_path)
    }).map_err(|_| {
        let error_msg = format!("Panic while opening database at: {}", db_path_str);
        error!("{}", error_msg);
        rusqlite::Error::SqliteFailure(
            rusqlite::ffi::Error::new(1), // SQLITE_ERROR
            Some(error_msg.into())
        )
    })??; // Unwrap the Result<Result<Connection, Error>, _>

    // Enable foreign key support
    connection_result.execute("PRAGMA foreign_keys = ON", [])?;
    
    info!("Successfully connected to database");
    Ok(connection_result)
}

pub fn establish_connection_with_path(db_path: &Path) -> Result<Connection> {
    Connection::open(db_path)
}

pub fn create_plugins_table(conn: &Connection) -> Result<()> {
    conn.execute(
        "CREATE TABLE IF NOT EXISTS plugins (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            path TEXT NOT NULL,
            description TEXT,
            version TEXT,
            enabled BOOLEAN NOT NULL DEFAULT 0
        )",
        [],
    )?;
    Ok(())
}

pub fn initialize_database(db_path: &Path) -> Result<()> {
    let conn = Connection::open(db_path)?;

    conn.execute(
        "CREATE TABLE IF NOT EXISTS files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            path TEXT NOT NULL UNIQUE,
            name TEXT NOT NULL,
            extension TEXT,
            last_modified INTEGER,
            size INTEGER
        )",
        [],
    )?;

    conn.execute(
        "CREATE TABLE IF NOT EXISTS index_entries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            file_id INTEGER NOT NULL,
            keyword TEXT NOT NULL,
            relevance REAL NOT NULL,
            FOREIGN KEY (file_id) REFERENCES files(id)
        )",
        [],
    )?;

    create_plugins_table(&conn)?;

    Ok(())
}

pub fn ensure_storage_dir_exists(storage_path: &Path) -> Result<()> {
    if !storage_path.exists() {
        fs::create_dir_all(storage_path)
            .map_err(|e| rusqlite::Error::SqliteFailure(
                rusqlite::ffi::Error::new(rusqlite::ffi::SQLITE_IOERR),
                Some(format!("Failed to create directory: {}", e))
            ))?;
    }
    Ok(())
}


pub fn insert_file(conn: &Connection, path: &str, name: &str, extension: Option<&str>, last_modified: i64, size: i64) -> Result<i64> {
    conn.execute(
        "INSERT INTO files (path, name, extension, last_modified, size) VALUES (?1, ?2, ?3, ?4, ?5)",
        &[path, name, &extension.unwrap_or(""), &last_modified.to_string(), &size.to_string()],
    )?;
    Ok(conn.last_insert_rowid())
}

pub fn insert_index_entry(conn: &Connection, keyword: &str, file_id: i64, relevance: f64) -> Result<()> {
    conn.execute(
        "INSERT INTO index_entries (keyword, file_id, relevance) VALUES (?1, ?2, ?3)",
        &[keyword, &file_id.to_string(), &relevance.to_string()],
    )?;
    Ok(())
}

pub fn delete_index_entries_for_file(conn: &Connection, file_id: i64) -> Result<()> {
    conn.execute("DELETE FROM index_entries WHERE file_id = ?1", &[&file_id.to_string()])?;
    Ok(())
}

pub fn get_file_by_path(conn: &Connection, file_path: &str) -> Result<Option<i64>> {
    let mut stmt = conn.prepare("SELECT id FROM files WHERE path = ?1")?;
    let mut rows = stmt.query([file_path])?;

    if let Some(row) = rows.next()? {
        Ok(Some(row.get(0)?))
    } else {
        Ok(None)
    }
}

pub fn update_file_content_preview(conn: &Connection, file_id: i64, content_preview: &str) -> Result<()> {
    conn.execute(
        "UPDATE files SET content_preview = ?1 WHERE id = ?2",
        rusqlite::params![content_preview, file_id],
    )?;
    Ok(())
}

pub fn insert_plugin(conn: &Connection, name: &str, description: Option<&str>, version: &str, enabled: bool, path: &str) -> Result<i64> {
    conn.execute(
        "INSERT INTO plugins (name, description, version, enabled, path) VALUES (?1, ?2, ?3, ?4, ?5)",
        rusqlite::params![name, description.unwrap_or(""), version, enabled, path],
    )?;
    Ok(conn.last_insert_rowid())
}

pub fn update_plugin(conn: &Connection, id: i64, name: &str, description: Option<&str>, version: &str, enabled: bool, path: &str) -> Result<()> {
    conn.execute(
        "UPDATE plugins SET name = ?1, description = ?2, version = ?3, enabled = ?4, path = ?5 WHERE id = ?6",
        rusqlite::params![name, description.unwrap_or(""), version, enabled, path, id],
    )?;
    Ok(())
}

pub fn get_plugin_by_path(conn: &Connection, path: &str) -> Result<Option<(i64, String, Option<String>, String, bool, String)>> {
    let mut stmt = conn.prepare("SELECT id, name, description, version, enabled, path FROM plugins WHERE path = ?1")?;
    let mut rows = stmt.query([path])?;

    if let Some(row) = rows.next()? {
        Ok(Some((
            row.get(0)?,
            row.get(1)?,
            row.get(2)?,
            row.get(3)?,
            row.get(4)?,
            row.get(5)?,
        )))
    } else {
        Ok(None)
    }
}

pub fn get_plugin_by_id(conn: &Connection, id: i64) -> Result<Option<(i64, String, Option<String>, String, bool, String)>> {
    let mut stmt = conn.prepare("SELECT id, name, description, version, enabled, path FROM plugins WHERE id = ?1")?;
    let mut rows = stmt.query([id])?;

    if let Some(row) = rows.next()? {
        Ok(Some((
            row.get(0)?,
            row.get(1)?,
            row.get(2)?,
            row.get(3)?,
            row.get(4)?,
            row.get(5)?,
        )))
    } else {
        Ok(None)
    }
}

pub fn get_all_plugins(conn: &Connection) -> Result<Vec<(i64, String, Option<String>, String, bool, String)>, rusqlite::Error> {
    let mut stmt = conn.prepare("SELECT id, name, description, version, enabled, path FROM plugins")?;
    let plugins_iter = stmt.query_map([], |row| {
        Ok((
            row.get(0)?,
            row.get(1)?,
            row.get(2)?,
            row.get(3)?,
            row.get(4)?,
            row.get(5)?,
        ))
    })?;

    let mut plugins = Vec::new();
    for plugin in plugins_iter {
        plugins.push(plugin?);
    }
    Ok(plugins)
}

pub fn update_plugin_enabled_status(conn: &Connection, name: &str, enabled: bool) -> Result<(), rusqlite::Error> {
    conn.execute(
        "UPDATE plugins SET enabled = ?1 WHERE name = ?2",
        &[&enabled as &dyn rusqlite::ToSql, &name as &dyn rusqlite::ToSql],
    )?;
    Ok(())
}

// Old save_chat_message function removed - replaced with new comprehensive version below

#[tauri::command]
pub async fn get_chat_messages() -> Result<Vec<String>, String> {
    // Placeholder: Simulate retrieving chat messages
    Ok(vec!["Chat message 1".to_string(), "Chat message 2".to_string()])
}

#[tauri::command]
pub async fn clear_chat_messages() -> Result<String, String> {
    // Placeholder: Simulate clearing chat messages
    Ok("Cleared all chat messages".to_string())
}

// Chat system database initialization
pub fn initialize_chat_tables() -> Result<(), rusqlite::Error> {
    let conn = establish_connection()?;

    // Create chat sessions table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS chat_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id TEXT UNIQUE NOT NULL,
            title TEXT NOT NULL,
            model_name TEXT NOT NULL,
            created_at INTEGER NOT NULL,
            updated_at INTEGER NOT NULL,
            message_count INTEGER DEFAULT 0
        )",
        [],
    )?;

    // Create chat messages table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS chat_messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id TEXT NOT NULL,
            message_id TEXT UNIQUE NOT NULL,
            sender TEXT NOT NULL, -- 'user' or 'assistant'
            content TEXT NOT NULL,
            timestamp INTEGER NOT NULL,
            model_used TEXT,
            FOREIGN KEY (session_id) REFERENCES chat_sessions (session_id)
        )",
        [],
    )?;

    // Create indexes for better performance
    conn.execute(
        "CREATE INDEX IF NOT EXISTS idx_messages_session_id ON chat_messages (session_id)",
        [],
    )?;

    conn.execute(
        "CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON chat_messages (timestamp)",
        [],
    )?;

    Ok(())
}

// Chat session management commands
#[tauri::command]
pub async fn save_chat_session(
    session_id: String,
    title: String,
    model_name: String
) -> Result<String, String> {
    let conn = establish_connection().map_err(|e| e.to_string())?;
    let timestamp = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_secs() as i64;

    conn.execute(
        "INSERT OR REPLACE INTO chat_sessions
         (session_id, title, model_name, created_at, updated_at)
         VALUES (?1, ?2, ?3, ?4, ?5)",
        &[&session_id, &title, &model_name, &timestamp.to_string(), &timestamp.to_string()],
    ).map_err(|e| e.to_string())?;

    Ok("Chat session saved successfully".to_string())
}

#[tauri::command]
pub async fn load_chat_sessions() -> Result<Vec<serde_json::Value>, String> {
    let conn = establish_connection().map_err(|e| e.to_string())?;
    let mut stmt = conn.prepare(
        "SELECT session_id, title, model_name, created_at, updated_at, message_count
         FROM chat_sessions ORDER BY updated_at DESC"
    ).map_err(|e| e.to_string())?;

    let session_iter = stmt.query_map([], |row| {
        Ok(serde_json::json!({
            "session_id": row.get::<_, String>(0)?,
            "title": row.get::<_, String>(1)?,
            "model_name": row.get::<_, String>(2)?,
            "created_at": row.get::<_, i64>(3)?,
            "updated_at": row.get::<_, i64>(4)?,
            "message_count": row.get::<_, i64>(5)?
        }))
    }).map_err(|e| e.to_string())?;

    let mut sessions = Vec::new();
    for session in session_iter {
        sessions.push(session.map_err(|e| e.to_string())?);
    }

    Ok(sessions)
}

#[tauri::command]
pub async fn save_chat_message(
    session_id: String,
    message_id: String,
    sender: String,
    content: String,
    model_used: Option<String>
) -> Result<String, String> {
    let conn = establish_connection().map_err(|e| e.to_string())?;
    let timestamp = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_secs() as i64;

    conn.execute(
        "INSERT INTO chat_messages
         (session_id, message_id, sender, content, timestamp, model_used)
         VALUES (?1, ?2, ?3, ?4, ?5, ?6)",
        &[&session_id, &message_id, &sender, &content, &timestamp.to_string(), &model_used.unwrap_or_default()],
    ).map_err(|e| e.to_string())?;

    // Update message count in session
    conn.execute(
        "UPDATE chat_sessions SET message_count = message_count + 1, updated_at = ?1
         WHERE session_id = ?2",
        &[&timestamp.to_string(), &session_id],
    ).map_err(|e| e.to_string())?;

    Ok("Message saved successfully".to_string())
}

#[tauri::command]
pub async fn load_chat_messages(session_id: String) -> Result<Vec<serde_json::Value>, String> {
    let conn = establish_connection().map_err(|e| e.to_string())?;
    let mut stmt = conn.prepare(
        "SELECT message_id, sender, content, timestamp, model_used
         FROM chat_messages WHERE session_id = ?1 ORDER BY timestamp ASC"
    ).map_err(|e| e.to_string())?;

    let message_iter = stmt.query_map([&session_id], |row| {
        Ok(serde_json::json!({
            "message_id": row.get::<_, String>(0)?,
            "sender": row.get::<_, String>(1)?,
            "content": row.get::<_, String>(2)?,
            "timestamp": row.get::<_, i64>(3)?,
            "model_used": row.get::<_, String>(4)?
        }))
    }).map_err(|e| e.to_string())?;

    let mut messages = Vec::new();
    for message in message_iter {
        messages.push(message.map_err(|e| e.to_string())?);
    }

    Ok(messages)
}

#[tauri::command]
pub async fn delete_chat_session(session_id: String) -> Result<String, String> {
    let conn = establish_connection().map_err(|e| e.to_string())?;

    // Delete messages first
    conn.execute(
        "DELETE FROM chat_messages WHERE session_id = ?1",
        &[&session_id],
    ).map_err(|e| e.to_string())?;

    // Delete session
    conn.execute(
        "DELETE FROM chat_sessions WHERE session_id = ?1",
        &[&session_id],
    ).map_err(|e| e.to_string())?;

    Ok("Chat session deleted successfully".to_string())
}
