# Documentation Guidelines

Effective documentation is vital for the long-term success, maintainability, and collaborative development of 'The Collective' application. This document outlines the guidelines for creating and maintaining various types of documentation within the project.

## Objectives of Documentation

*   **Knowledge Transfer:** Facilitate onboarding of new team members and ensure continuity of knowledge.
*   **Maintainability:** Help developers understand existing code, making it easier to debug, modify, and extend.
*   **Collaboration:** Provide a common understanding of the system for all stakeholders.
*   **Troubleshooting:** Aid in diagnosing and resolving issues.
*   **Feature Understanding:** Clearly explain how features work and how to use them.

## Types of Documentation

We will maintain the following types of documentation:

1.  **Code Comments:**
    *   **Purpose:** Explain complex logic, algorithms, non-obvious design choices, and the "why" behind certain implementations.
    *   **Location:** Directly within the source code files.
    *   **Guidelines:**
        *   **Rust (`src-tauri`):** Use Rustdoc comments (`///` for public items, `//` for internal comments). Document functions, structs, enums, and modules.
        *   **React/TypeScript (`assistant-ui`):** Use JSDoc-style comments for functions, components, and complex logic. Explain props, state, and component lifecycle.
        *   **Be Concise and Clear:** Avoid restating the obvious. Focus on explaining *what* the code does if it's not immediately apparent, and *why* it does it that way.
        *   **Keep Up-to-Date:** Comments must be updated whenever the code they describe changes.

2.  **API Documentation:**
    *   **Purpose:** Describe the interfaces and contracts between different parts of the system, especially between the frontend and the Rust backend (Tauri commands, event listeners).
    *   **Location:** Can be generated from code comments (e.g., Rustdoc) or maintained in separate markdown files.
    *   **Guidelines:**
        *   Clearly define input parameters, return types, and potential errors for each API endpoint or command.
        *   Provide examples of usage.
        *   Document any side effects or preconditions.

3.  **Architectural Documentation:**
    *   **Purpose:** Provide a high-level overview of the system's design, major components, their interactions, data flow, and key architectural decisions.
    *   **Location:** Dedicated markdown files (e.g., `docs/architecture.md`).
    *   **Guidelines:**
        *   Use diagrams (e.g., Mermaid, PlantUML) to visualize components and their relationships.
        *   Explain the rationale behind significant design choices (e.g., why Tauri/Rust was chosen, how LLM integration works).
        *   Describe the overall system boundaries and external dependencies.

4.  **README Files:**
    *   **Purpose:** Provide essential information for getting started with a specific part of the project.
    *   **Location:** Root of the project (`README.md`) and within major subdirectories (e.g., `assistant-ui/README.md`, `src-tauri/README.md`).
    *   **Guidelines:**
        *   **Project Root `README.md`:** High-level project description, setup instructions, how to run the application, key features, and links to other important documentation.
        *   **Subdirectory `README.md`s:** Specific setup, build, and run instructions for that module/component. Explain its purpose and how it fits into the larger system.
        *   Include information on dependencies and prerequisites.

5.  **Troubleshooting Guides/FAQs:**
    *   **Purpose:** Document common issues, their symptoms, and steps to resolve them.
    *   **Location:** Dedicated markdown files (e.g., `docs/troubleshooting.md`).
    *   **Guidelines:**
        *   Categorize issues for easy navigation.
        *   Provide clear, step-by-step solutions.
        *   Include error messages or logs that might indicate the problem.

## Documentation Workflow

*   **"Documentation as Code":** Treat documentation like code. Store it in the same repository, version control it, and include it in code reviews.
*   **Update Regularly:** Documentation should be updated as part of the development process, not as an afterthought. If code changes, update the relevant documentation.
*   **Review Documentation:** Include documentation changes in code reviews to ensure accuracy, clarity, and completeness.
*   **Accessibility:** Ensure documentation is easily discoverable and readable.

By following these guidelines, we aim to create a comprehensive and up-to-date documentation suite that supports efficient development and long-term maintenance of 'The Collective'.