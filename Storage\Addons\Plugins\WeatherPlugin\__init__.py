# Weather Plugin for The Collective

NAME = "Weather Plugin"
DESCRIPTION = "Provides weather information and forecasts"
VERSION = "0.1.0"

def get_weather(location):
    """
    Get current weather for a location
    This is a placeholder implementation
    """
    # In a real implementation, this would call a weather API
    return {
        "location": location,
        "temperature": "22°C",
        "condition": "Partly Cloudy",
        "humidity": "65%",
        "wind": "5 km/h"
    }

def get_forecast(location, days=5):
    """
    Get weather forecast for a location
    This is a placeholder implementation
    """
    # In a real implementation, this would call a weather API
    forecast = []
    for i in range(days):
        forecast.append({
            "day": f"Day {i+1}",
            "temperature": f"{20 + i}°C",
            "condition": "Sunny"
        })
    return forecast

# Register plugin functions
def register_functions():
    return {
        "get_weather": get_weather,
        "get_forecast": get_forecast
    }