{"version": "1.0", "lastUpdated": "2025-01-26", "description": "Specialized AI agents for specific tasks", "cascadeLevel": 3, "dependsOn": ["systemPrompts", "modals"], "items": [{"id": "codeReviewer", "file": "codeReviewer.json", "name": "Code Reviewer", "description": "Specialized agent for code review and analysis", "category": "development", "triggers": ["review", "analyze", "check"], "active": true, "requiresPrompts": ["coding"], "optionalModals": ["debug"], "conditions": ["file_type:code", "user_intent:review"]}, {"id": "dataAnalyst", "file": "dataAnalyst.json", "name": "Data Analyst", "description": "Specialized agent for data analysis and insights", "category": "analytics", "triggers": ["analyze", "data", "statistics", "insights"], "active": true, "requiresPrompts": ["general"], "optionalModals": ["explain"], "conditions": ["data_present", "analysis_requested"]}, {"id": "projectManager", "file": "projectManager.json", "name": "Project Manager", "description": "Specialized agent for project planning and management", "category": "management", "triggers": ["plan", "manage", "organize", "timeline"], "active": true, "requiresPrompts": ["general"], "optionalModals": ["concise"], "conditions": ["project_context", "planning_phase"]}]}