import React, { useState, useEffect, useCallback, memo } from 'react';
import { Button } from "./ui/button";
import ErrorPopup from './ErrorPopup';
import { useIsMounted } from '../hooks/useIsMounted.js';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus, faTimes, faGlobe, faComments, faFolder, faNoteSticky, faCloud, faWrench, faCode } from '@fortawesome/free-solid-svg-icons';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { invoke } from '@tauri-apps/api/core';

// Simple ErrorBoundary component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('TabSystem Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <ErrorPopup
          title="Tab System Error"
          message="An error occurred in the tab system. Please try reloading the page."
          error={this.state.error}
          onReload={() => window.location.reload()}
        />
      );
    }

    return this.props.children;
  }
}

const TabSystem = memo(({ tabs, addTab, closeTab }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [showAppMenu, setShowAppMenu] = useState(false);
  const [availableApps, setAvailableApps] = useState([]);
  const [loading, setLoading] = useState(true);
  const isMounted = useIsMounted();

  // Load available apps from plugin system
  useEffect(() => {
    const loadAvailableApps = async () => {
      try {
        if (!isMounted()) return;
        if (isMounted()) {
          setLoading(true);
        }
        
        // Get all plugins from the backend
        const plugins = await invoke('get_plugins');
        
        if (!isMounted()) return;
        
        const apps = plugins
          .filter(plugin => plugin.enabled && (plugin.plugin_type === 'ui_component' || plugin.app_type))
          .map(plugin => {
            // Map plugin names to appropriate icons
            let icon;
            switch (plugin.name.toLowerCase()) {
              case 'chat':
                icon = faComments;
                break;
              case 'file explorer':
                icon = faFolder;
                break;
              case 'browser':
                icon = faGlobe;
                break;
              case 'notes':
                icon = faNoteSticky;
                break;
              case 'weather':
                icon = faCloud;
                break;
              default:
                icon = plugin.plugin_type === 'python' ? faCode : faWrench;
            }
            
            return {
              id: plugin.name.toLowerCase().replace(/\s+/g, '-'),
              name: plugin.name,
              icon: icon,
              component: plugin.name.replace(/\s+/g, ''),
              allowMultiple: true,
              app_type: plugin.app_type || 'app',
              plugin_type: plugin.plugin_type,
              description: plugin.description
            };
          });
          
        if (isMounted()) {
          setAvailableApps(apps);
          setLoading(false);
        }
      } catch (error) {
        console.error('Failed to load available apps:', error);
        // Fallback to hardcoded apps if backend fails
        if (isMounted()) {
          setAvailableApps([
            { id: 'browser', name: 'Browser', icon: faGlobe, component: 'Browser', allowMultiple: true, app_type: 'core-tool' },
            { id: 'chat', name: 'Chat', icon: faComments, component: 'Chat', allowMultiple: true, app_type: 'core-tool' },
            { id: 'file-explorer', name: 'File Explorer', icon: faFolder, component: 'FileExplorer', allowMultiple: true, app_type: 'core-tool' },
          ]);
          setLoading(false);
        }
      }
    };

    loadAvailableApps();
  }, [isMounted]);

  const getAvailableApps = useCallback(() => {
    return availableApps;
  }, [availableApps]);

  const handleCloseTab = useCallback((e, tabId) => {
    e.stopPropagation();
    e.preventDefault();
    
    // Ensure we have valid parameters before calling closeTab
    if (!tabId) {
      console.warn('Invalid tabId for closing:', tabId);
      return;
    }
    
    // Call closeTab with only the tabId (matching App.jsx expectation)
    if (typeof closeTab === 'function') {
      // Use setTimeout with 0 delay to prevent race conditions with DOM updates
      // This gives the browser a chance to finish any pending DOM operations
      setTimeout(() => {
        if (isMounted()) {
          closeTab(tabId);
        }
      }, 0);
    }
  }, [closeTab, isMounted]);

  return (
    <ErrorBoundary>
      <div className="flex items-center bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 px-4 z-50">
        {tabs
          .filter(tab => tab && tab.id && tab.path && tab.name) // Defensive filtering
          .map((tab) => (
          <Link
            key={tab.id} // Simplified key to prevent reconciliation issues
            to={tab.path}
            className={`flex items-center px-3 py-1.5 cursor-pointer transition-all duration-200 relative group ${
              location.pathname === tab.path
                ? 'text-blue-600 dark:text-blue-400'
                : 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-200'
            }`}
            onClick={(e) => {
              // Prevent default navigation and use navigate function to ensure proper routing
              e.preventDefault();
              // Use setTimeout with 0 delay to prevent race conditions
              setTimeout(() => {
                if (isMounted()) {
                  navigate(tab.path);
                }
              }, 0);
            }}
          >
            {location.pathname === tab.path && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500"></div>
            )}
            <FontAwesomeIcon icon={tab.icon} className="mr-1.5 h-3 w-3" />
            <span className="text-xs font-medium">{tab.name}</span>
            {/* Only show close button for non-core tabs */}
            {tab.id !== 'dashboard' && tab.id !== 'chat' && tab.id !== 'tools' && tab.id !== 'settings' && (
              <Button
                variant="ghost"
                size="sm"
                className="ml-2 h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-slate-200 dark:hover:bg-slate-700"
                onClick={(e) => handleCloseTab(e, tab.id)}
              >
                <FontAwesomeIcon icon={faTimes} className="h-3 w-3" />
              </Button>
            )}
          </Link>
        ))}
        <div className="relative ml-2">
          <Button
            variant="ghost"
            size="sm"
            className="px-3 py-2 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 hover:bg-slate-200 dark:hover:bg-slate-700 transition-colors"
            onClick={() => {
              if (isMounted()) {
                setShowAppMenu(!showAppMenu);
              }
            }}
          >
            <FontAwesomeIcon icon={faPlus} className="h-4 w-4" />
          </Button>
          {showAppMenu && (
            <div className="absolute top-full left-0 mt-2 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-600 rounded-lg shadow-xl z-50 min-w-[220px] max-w-[300px] overflow-hidden">
              <div className="py-2">
                <div className="px-3 py-2 text-xs font-semibold text-slate-500 dark:text-slate-400 uppercase tracking-wider border-b border-slate-100 dark:border-slate-700">
                  Add Application
                </div>
                
                {loading ? (
                  <div className="flex items-center justify-center py-6">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                    <span className="ml-2 text-sm text-slate-500 dark:text-slate-400">Loading apps...</span>
                  </div>
                ) : (
                  <div className="max-h-96 overflow-y-auto">
                    {/* Core Tools */}
                    {getAvailableApps().filter(app => app.app_type === 'core-tool').length > 0 && (
                      <>
                        <div className="px-3 py-2 text-xs font-medium text-slate-600 dark:text-slate-300 bg-slate-50 dark:bg-slate-700/50">
                          Core Tools
                        </div>
                        {getAvailableApps()
                          .filter(app => app.app_type === 'core-tool')
                          .map((app) => (
                            <button
                              key={`app-${app.id}`} // Stable key for app menu items
                              className="flex items-start w-full px-4 py-3 text-left hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors text-slate-700 dark:text-slate-300"
                              onClick={() => {
                                if (isMounted()) {
                                  addTab(app);
                                  setShowAppMenu(false);
                                }
                              }}
                            >
                              <FontAwesomeIcon icon={app.icon} className="mr-3 h-4 w-4 text-blue-500 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                              <div className="flex-1 min-w-0">
                                <div className="font-medium truncate">{app.name}</div>
                                {app.description && (
                                  <div className="text-xs text-slate-500 dark:text-slate-400 mt-1 line-clamp-2">
                                    {app.description}
                                  </div>
                                )}
                              </div>
                            </button>
                          ))
                        }
                      </>
                    )}
                    
                    {/* Other Apps */}
                    {getAvailableApps().filter(app => app.app_type !== 'core-tool').length > 0 && (
                      <>
                        <div className="px-3 py-2 text-xs font-medium text-slate-600 dark:text-slate-300 bg-slate-50 dark:bg-slate-700/50">
                          Applications
                        </div>
                        {getAvailableApps()
                          .filter(app => app.app_type !== 'core-tool')
                          .map((app) => (
                            <button
                              key={`app-${app.id}`} // Stable key for app menu items
                              className="flex items-start w-full px-4 py-3 text-left hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors text-slate-700 dark:text-slate-300"
                              onClick={() => {
                                if (isMounted()) {
                                  addTab(app);
                                  setShowAppMenu(false);
                                }
                              }}
                            >
                              <FontAwesomeIcon 
                                icon={app.icon} 
                                className={`mr-3 h-4 w-4 mt-0.5 flex-shrink-0 ${
                                  app.plugin_type === 'python' ? 'text-yellow-500' : 'text-slate-500 dark:text-slate-400'
                                }`} 
                              />
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2">
                                  <span className="font-medium truncate">{app.name}</span>
                                  {app.plugin_type === 'python' && (
                                    <span className="text-xs bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 px-1.5 py-0.5 rounded">
                                      Python
                                    </span>
                                  )}
                                </div>
                                {app.description && (
                                  <div className="text-xs text-slate-500 dark:text-slate-400 mt-1 line-clamp-2">
                                    {app.description}
                                  </div>
                                )}
                              </div>
                            </button>
                          ))
                        }
                      </>
                    )}
                    
                    {getAvailableApps().length === 0 && (
                      <div className="px-4 py-6 text-center text-slate-500 dark:text-slate-400">
                        <FontAwesomeIcon icon={faWrench} className="h-8 w-8 mb-2 opacity-50" />
                        <div className="text-sm">No applications available</div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </ErrorBoundary>
  );
});

TabSystem.displayName = 'TabSystem';

export default memo(TabSystem);