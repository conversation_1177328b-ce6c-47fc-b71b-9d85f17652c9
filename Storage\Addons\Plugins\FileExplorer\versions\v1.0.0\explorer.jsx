import React, { useState, useEffect, useRef } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { Card, CardContent, CardHeader, CardTitle } from "./components/ui/card.jsx";
import { Button } from './components/ui/button.jsx';
import { Input } from './components/ui/input.jsx';
import { Badge } from './components/ui/badge.jsx';
import { FontAwesomeIcon } from './components/icons/FontAwesome.jsx';
import { 
  faFolder, 
  faFile, 
  faSearch, 
  faHome, 
  faArrowLeft, 
  faArrowRight, 
  faArrowUp,
  faList,
  faThLarge,
  faStar,
  faClock,
  faBookmark,
  faCog,
  faRefresh
} from './components/icons/FontAwesome.jsx';

const FileExplorer = ({ addTab }) => {
  const [currentPath, setCurrentPath] = useState('');
  const [directories, setDirectories] = useState([]);
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState('list'); // 'list' or 'grid'
  const [selectedItems, setSelectedItems] = useState([]);
  const [navigationHistory, setNavigationHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [bookmarks, setBookmarks] = useState([]);
  const [recentLocations, setRecentLocations] = useState([]);
  const [showHidden, setShowHidden] = useState(false);
  const [isMounted, setIsMounted] = useState(true);

  useEffect(() => {
    // Initialize file explorer
    initializeFileExplorer();
    loadBookmarks();
    loadRecentLocations();
    loadPluginState();

    // Cleanup function
    return () => {
      setIsMounted(false);
    };
  }, []);

  useEffect(() => {
    if (currentPath && isMounted) {
      browseDirectory(currentPath, false);
    }
  }, [currentPath, showHidden, isMounted]);

  useEffect(() => {
    // Auto-save state when important changes occur
    if (isMounted) {
      savePluginState();
    }
  }, [currentPath, viewMode, selectedItems, showHidden, isMounted]);

  const initializeFileExplorer = async () => {
    try {
      // Load saved settings for this plugin
      const settings = await invoke('get_plugin_settings', { plugin_name: 'FileExplorer' });
      if (settings && isMounted) {
        setViewMode(settings.default_view || 'list');
        setShowHidden(settings.show_hidden_files || false);
      }

      // Set initial path to user's home directory or a default path
      const initialPath = settings?.default_path || 'C:\\';
      if (isMounted) {
        setCurrentPath(initialPath);
      }
    } catch (error) {
      console.error('Error initializing file explorer:', error);
      if (isMounted) {
        setCurrentPath('C:\\');
      }
    }
  };

  const loadBookmarks = async () => {
    try {
      const savedBookmarks = await invoke('get_plugin_data', { 
        plugin_name: 'FileExplorer',
        data_key: 'bookmarks'
      });
      if (savedBookmarks && isMounted) {
        setBookmarks(JSON.parse(savedBookmarks));
      }
    } catch (error) {
      console.error('Error loading bookmarks:', error);
    }
  };

  const loadRecentLocations = async () => {
    try {
      const savedRecent = await invoke('get_plugin_data', { 
        plugin_name: 'FileExplorer',
        data_key: 'recent_locations'
      });
      if (savedRecent && isMounted) {
        setRecentLocations(JSON.parse(savedRecent));
      }
    } catch (error) {
      console.error('Error loading recent locations:', error);
    }
  };

  const browseDirectory = async (path, addToHistory = true) => {
    try {
      if (!isMounted) return;
      
      setLoading(true);
      setError(null);
      
      const result = await invoke('browse_directory', { path });
      
      if (!isMounted) return;
      
      setDirectories(result.directories || []);
      setFiles(result.files || []);
      
      if (addToHistory && path !== currentPath) {
        // Add to navigation history
        const newHistory = navigationHistory.slice(0, historyIndex + 1);
        newHistory.push(path);
        setNavigationHistory(newHistory);
        setHistoryIndex(newHistory.length - 1);
        
        // Add to recent locations
        addToRecentLocations(path);
      }
      
      setCurrentPath(path);
      setSelectedItems([]);
    } catch (error) {
      console.error('Error browsing directory:', error);
      if (isMounted) {
        setError(`Failed to browse directory: ${error.message}`);
      }
    } finally {
      if (isMounted) {
        setLoading(false);
      }
    }
  };

  const addToRecentLocations = async (path) => {
    try {
      if (!isMounted) return;
      
      const newRecent = [path, ...recentLocations.filter(p => p !== path)].slice(0, 20);
      setRecentLocations(newRecent);
      
      await invoke('save_plugin_data', {
        plugin_name: 'FileExplorer',
        data_key: 'recent_locations',
        data_value: JSON.stringify(newRecent)
      });
    } catch (error) {
      console.error('Error saving recent locations:', error);
    }
  };

  const navigateUp = () => {
    if (currentPath) {
      const parentPath = currentPath.split(/[\\/]/).slice(0, -1).join('\\');
      if (parentPath && parentPath !== currentPath) {
        browseDirectory(parentPath + '\\');
      }
    }
  };

  const navigateBack = () => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);
      browseDirectory(navigationHistory[newIndex], false);
    }
  };

  const navigateForward = () => {
    if (historyIndex < navigationHistory.length - 1) {
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);
      browseDirectory(navigationHistory[newIndex], false);
    }
  };

  const toggleBookmark = async (path) => {
    try {
      if (!isMounted) return;
      const isBookmarked = bookmarks.some(b => b.path === path);
      let newBookmarks;
      
      if (isBookmarked) {
        newBookmarks = bookmarks.filter(b => b.path !== path);
      } else {
        const bookmark = {
          path,
          name: path.split(/[\\/]/).pop() || path,
          added_at: new Date().toISOString()
        };
        newBookmarks = [...bookmarks, bookmark];
      }
      
      setBookmarks(newBookmarks);
      await invoke('save_plugin_data', {
        plugin_name: 'FileExplorer',
        data_key: 'bookmarks',
        data_value: JSON.stringify(newBookmarks)
      });
    } catch (error) {
      console.error('Error toggling bookmark:', error);
    }
  };

  const handleItemClick = (item, isDirectory) => {
    if (isDirectory) {
      browseDirectory(item.path);
    } else {
      // Handle file opening logic here
      console.log('Opening file:', item.path);
    }
  };

  const handleItemSelect = (item) => {
    setSelectedItems(prev => {
      if (prev.includes(item)) {
        return prev.filter(i => i !== item);
      } else {
        return [...prev, item];
      }
    });
  };

  const savePluginState = async () => {
    try {
      const state = {
        currentPath,
        viewMode,
        showHidden,
        navigationHistory: navigationHistory.slice(-10), // Keep last 10 for performance
        historyIndex,
        selectedItems: selectedItems.slice(0, 5), // Keep only first 5 selected items
        lastActivity: new Date().toISOString()
      };
      
      await invoke('save_plugin_state', {
        plugin_name: 'FileExplorer',
        state_data: JSON.stringify(state)
      });
    } catch (error) {
      console.error('Error saving plugin state:', error);
    }
  };

  const loadPluginState = async () => {
    try {
      const stateData = await invoke('load_plugin_state', { plugin_name: 'FileExplorer' });
      if (stateData && stateData !== '{}' && isMounted) {
        const state = JSON.parse(stateData);
        if (state.currentPath) {
          setCurrentPath(state.currentPath);
        }
        if (state.viewMode) {
          setViewMode(state.viewMode);
        }
        if (typeof state.showHidden === 'boolean') {
          setShowHidden(state.showHidden);
        }
        if (state.navigationHistory) {
          setNavigationHistory(state.navigationHistory);
          setHistoryIndex(state.historyIndex || state.navigationHistory.length - 1);
        }
      }
    } catch (error) {
      console.error('Error loading plugin state:', error);
    }
  };

  const filteredDirectories = directories.filter(dir => 
    dir.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
    (showHidden || !dir.name.startsWith('.'))
  );

  const filteredFiles = files.filter(file => 
    file.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
    (showHidden || !file.name.startsWith('.'))
  );

  const formatFileSize = (size) => {
    if (size < 1024) return `${size} B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
    if (size < 1024 * 1024 * 1024) return `${(size / (1024 * 1024)).toFixed(1)} MB`;
    return `${(size / (1024 * 1024 * 1024)).toFixed(1)} GB`;
  };

  const renderListView = () => (
    <div className="space-y-1">
      {filteredDirectories.map((dir) => (
        <div
          key={`dir-${dir.path || dir.name}`}
          className={`flex items-center gap-3 p-2 rounded cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-700 ${
            selectedItems.includes(dir) ? 'bg-blue-100 dark:bg-blue-900/30' : ''
          }`}
          onClick={() => handleItemClick(dir, true)}
          onContextMenu={(e) => {
            e.preventDefault();
            handleItemSelect(dir);
          }}
        >
          <FontAwesomeIcon icon={faFolder} className="h-5 w-5 text-blue-500" />
          <span className="flex-1 text-sm">{dir.name}</span>
          <span className="text-xs text-slate-500">Folder</span>
        </div>
      ))}
      
      {filteredFiles.map((file) => (
        <div
          key={`file-${file.path || file.name}`}
          className={`flex items-center gap-3 p-2 rounded cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-700 ${
            selectedItems.includes(file) ? 'bg-blue-100 dark:bg-blue-900/30' : ''
          }`}
          onClick={() => handleItemClick(file, false)}
          onContextMenu={(e) => {
            e.preventDefault();
            handleItemSelect(file);
          }}
        >
          <FontAwesomeIcon icon={faFile} className="h-5 w-5 text-slate-500" />
          <span className="flex-1 text-sm">{file.name}</span>
          <span className="text-xs text-slate-500">
            {file.size ? formatFileSize(file.size) : 'File'}
          </span>
        </div>
      ))}
    </div>
  );

  const renderGridView = () => (
    <div className="grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-4">
      {filteredDirectories.map((dir) => (
        <Card
          key={`dir-${dir.path || dir.name}`}
          className={`cursor-pointer hover:shadow-md transition-shadow ${
            selectedItems.includes(dir) ? 'ring-2 ring-blue-500' : ''
          }`}
          onClick={() => handleItemClick(dir, true)}
          onContextMenu={(e) => {
            e.preventDefault();
            handleItemSelect(dir);
          }}
        >
          <CardContent className="p-3 text-center">
            <FontAwesomeIcon icon={faFolder} className="h-8 w-8 text-blue-500 mb-2" />
            <p className="text-xs truncate">{dir.name}</p>
          </CardContent>
        </Card>
      ))}
      
      {filteredFiles.map((file) => (
        <Card
          key={`file-${file.path || file.name}`}
          className={`cursor-pointer hover:shadow-md transition-shadow ${
            selectedItems.includes(file) ? 'ring-2 ring-blue-500' : ''
          }`}
          onClick={() => handleItemClick(file, false)}
          onContextMenu={(e) => {
            e.preventDefault();
            handleItemSelect(file);
          }}
        >
          <CardContent className="p-3 text-center">
            <FontAwesomeIcon icon={faFile} className="h-8 w-8 text-slate-500 mb-2" />
            <p className="text-xs truncate">{file.name}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  return (
    <div className="file-explorer h-full flex bg-slate-50 dark:bg-slate-900">
      {/* Sidebar */}
      <div className="w-64 bg-white dark:bg-slate-800 border-r border-slate-200 dark:border-slate-700 p-4">
        <div className="space-y-4">
          {/* Quick Access */}
          <div>
            <h3 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Quick Access</h3>
            <div className="space-y-1">
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start"
                onClick={() => browseDirectory('C:\\')}
              >
                <FontAwesomeIcon icon={faHome} className="h-4 w-4 mr-2" />
                Home
              </Button>
            </div>
          </div>

          {/* Bookmarks */}
          <div>
            <h3 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Bookmarks</h3>
            <div className="space-y-1 max-h-32 overflow-y-auto">
              {bookmarks.map((bookmark) => (
                <Button
                  key={`bookmark-${bookmark.path}`}
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start text-xs"
                  onClick={() => browseDirectory(bookmark.path)}
                >
                  <FontAwesomeIcon icon={faBookmark} className="h-3 w-3 mr-2" />
                  {bookmark.name}
                </Button>
              ))}
            </div>
          </div>

          {/* Recent Locations */}
          <div>
            <h3 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Recent</h3>
            <div className="space-y-1 max-h-32 overflow-y-auto">
              {recentLocations.slice(0, 5).map((location) => (
                <Button
                  key={`recent-${location}`}
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start text-xs"
                  onClick={() => browseDirectory(location)}
                >
                  <FontAwesomeIcon icon={faClock} className="h-3 w-3 mr-2" />
                  {location.split(/[\\/]/).pop() || location}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-3">
              <FontAwesomeIcon icon={faFolder} className="h-6 w-6 text-blue-500" />
              <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">
                File Explorer
              </h1>
            </div>
            
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={() => addTab({ id: 'settings', name: 'Settings', icon: faCog })}>
                Settings
              </Button>
            </div>
          </div>

          {/* Navigation Bar */}
          <div className="flex items-center gap-2 mb-3">
            <Button
              variant="outline"
              size="sm"
              onClick={navigateBack}
              disabled={historyIndex <= 0}
            >
              <FontAwesomeIcon icon={faArrowLeft} className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={navigateForward}
              disabled={historyIndex >= navigationHistory.length - 1}
            >
              <FontAwesomeIcon icon={faArrowRight} className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={navigateUp}
            >
              <FontAwesomeIcon icon={faArrowUp} className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => browseDirectory(currentPath)}
            >
              <FontAwesomeIcon icon={faRefresh} className="h-4 w-4" />
            </Button>
            
            <div className="flex-1 mx-4">
              <Input
                type="text"
                value={currentPath}
                onChange={(e) => setCurrentPath(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    browseDirectory(currentPath);
                  }
                }}
                className="w-full"
                placeholder="Enter path..."
              />
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => toggleBookmark(currentPath)}
            >
              <FontAwesomeIcon 
                icon={faStar} 
                className={`h-4 w-4 ${bookmarks.some(b => b.path === currentPath) ? 'text-yellow-500' : ''}`} 
              />
            </Button>
          </div>

          {/* Toolbar */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="relative">
                <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Search files..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <FontAwesomeIcon icon={faList} className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <FontAwesomeIcon icon={faThLarge} className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 overflow-y-auto p-4">
          {loading && (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-slate-600 dark:text-slate-400">Loading directory...</p>
            </div>
          )}
          
          {error && (
            <div className="text-center py-8">
              <p className="text-red-500 bg-red-100 dark:bg-red-900/30 p-4 rounded-lg max-w-2xl mx-auto">
                {error}
              </p>
            </div>
          )}

          {!loading && !error && (
            <div>
              {filteredDirectories.length === 0 && filteredFiles.length === 0 ? (
                <div className="text-center py-12">
                  <FontAwesomeIcon icon={faFolder} className="h-16 w-16 text-slate-400 mb-4" />
                  <p className="text-slate-600 dark:text-slate-400 text-lg">
                    {searchTerm ? 'No files found' : 'This folder is empty'}
                  </p>
                </div>
              ) : (
                viewMode === 'list' ? renderListView() : renderGridView()
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FileExplorer;