import React, { useState, useEffect, useRef } from 'react';
import { Button } from "./components/ui/button";
import { Input } from "./components/ui/input";
import { FontAwesomeIcon } from './components/icons/FontAwesome.jsx';
import {
  faArrowLeft,
  faArrowRight,
  faRefresh,
  faHome,
  faBookmark,
  faHistory,
  faShieldAlt,
  faDownload,
  faBars,
  faExternalLinkAlt,
  faCog
} from './components/icons/FontAwesome.jsx';

// Browser component adapted for plugin system
const Browser = ({ addTab }) => {
  const settingsMenuRef = useRef(null);
  const [url, setUrl] = useState('https://www.google.com');
  const [currentUrl, setCurrentUrl] = useState('https://www.google.com');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    console.log('Browser mounted or currentUrl changed:', currentUrl);
  }, [currentUrl]);

  useEffect(() => {
    console.log('Browser isLoading changed:', isLoading);
  }, [isLoading]);
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [bookmarks, setBookmarks] = useState([]);
  const [showBookmarks, setShowBookmarks] = useState(false);
  const [showSettingsMenu, setShowSettingsMenu] = useState(false);
  const [browserSettings, setBrowserSettings] = useState({
    blockPopups: true,
    enableJavaScript: true,
    enableImages: true,
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    enableCookies: true,
    enableCache: true,
    zoomLevel: 100,
    homepage: 'https://www.google.com'
  });
  const [loadError, setLoadError] = useState(false);

  // Load initial data
  useEffect(() => {
    setBookmarks([
      { id: '1', title: 'Google', url: 'https://www.google.com' },
      { id: '2', title: 'GitHub', url: 'https://github.com' }
    ]);
  }, []);

  // Close settings menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (settingsMenuRef.current && !settingsMenuRef.current.contains(event.target)) {
        setShowSettingsMenu(false);
      }
    };

    if (showSettingsMenu) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showSettingsMenu]);

  const handleNavigate = () => {
    if (!url.trim()) return;

    let formattedUrl = url;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      formattedUrl = 'https://' + url;
    }

    setIsLoading(true);
    setLoadError(false);
    setCurrentUrl(formattedUrl);

    // Add to history
    const newHistory = [...history];
    if (historyIndex < newHistory.length - 1) {
      newHistory.splice(historyIndex + 1);
    }
    newHistory.push(formattedUrl);
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);

    // Set a timeout to show error if page doesn't load
    setTimeout(() => {
      if (isLoading) {
        setIsLoading(false);
        setLoadError(true);
      }
    }, 10000); // 10 second timeout
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleNavigate();
    }
  };

  const goBack = () => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);
      const prevUrl = history[newIndex];
      setCurrentUrl(prevUrl);
      setUrl(prevUrl);
    }
  };

  const goForward = () => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);
      const nextUrl = history[newIndex];
      setCurrentUrl(nextUrl);
      setUrl(nextUrl);
    }
  };

  const refresh = () => {
    setIsLoading(true);
    // Force iframe reload by changing src temporarily
    try {
      const iframe = document.querySelector('.browser-iframe');
      if (iframe && iframe.parentNode && iframe.parentNode.contains(iframe)) {
        const currentSrc = iframe.src;
        iframe.src = 'about:blank';
        setTimeout(() => {
          // Check if iframe still exists before manipulating
          if (iframe && iframe.parentNode && iframe.parentNode.contains(iframe)) {
            iframe.src = currentSrc;
          }
          setIsLoading(false);
        }, 100);
      } else {
        setIsLoading(false);
      }
    } catch (error) {
      console.warn('Error refreshing iframe:', error);
      setIsLoading(false);
    }
  };

  const goHome = () => {
    const homepage = browserSettings.homepage;
    setUrl(homepage);
    setCurrentUrl(homepage);
    setLoadError(false);
  };

  const addBookmark = () => {
    const newBookmark = {
      id: Date.now().toString(),
      title: currentUrl,
      url: currentUrl,
      created_at: new Date().toISOString()
    };
    
    setBookmarks(prev => [...prev, newBookmark]);
  };

  const navigateToBookmark = (bookmark) => {
    setUrl(bookmark.url);
    setCurrentUrl(bookmark.url);
    setShowBookmarks(false);
    setLoadError(false);
  };

  const openInExternalBrowser = () => {
    // In a real Tauri app, you would use the shell plugin
    // For now, we'll show a message
    alert(`Opening ${currentUrl} in external browser...`);
    // TODO: Implement with Tauri shell plugin
    // invoke('open_external', { url: currentUrl });
  };

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Browser Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <h1 className="text-2xl font-bold">Browser</h1>
        {addTab && (
          <Button variant="outline" size="sm" onClick={() => addTab({ id: 'settings', name: 'Settings', icon: faCog })}>
            <FontAwesomeIcon icon={faCog} className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Browser Controls */}
      <div className="flex items-center p-2 bg-card border-b border-border space-x-2">
        {/* Navigation Buttons */}
        <div className="flex space-x-1">
          <Button 
            variant="outline" 
            size="sm"
            onClick={goBack}
            disabled={historyIndex <= 0}
            title="Go Back"
          >
            <FontAwesomeIcon icon={faArrowLeft} className="h-4 w-4" />
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={goForward}
            disabled={historyIndex >= history.length - 1}
            title="Go Forward"
          >
            <FontAwesomeIcon icon={faArrowRight} className="h-4 w-4" />
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={refresh}
            disabled={isLoading}
            title="Refresh"
          >
            <FontAwesomeIcon icon={faRefresh} className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={goHome}
            title="Home"
          >
            <FontAwesomeIcon icon={faHome} className="h-4 w-4" />
          </Button>
        </div>

        {/* Address Bar */}
        <div className="flex-1 flex items-center space-x-2">
          <div className="flex-1 relative">
            <Input
              type="text"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              onKeyPress={handleKeyPress}
              className="pr-8"
              placeholder="Enter URL or search..."
              disabled={isLoading}
            />
            {isLoading && (
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                <FontAwesomeIcon icon={faShieldAlt} className="h-4 w-4 text-green-500" />
              </div>
            )}
          </div>
          <Button 
            onClick={handleNavigate} 
            size="sm"
            disabled={isLoading || !url.trim()}
          >
            Go
          </Button>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-1">
          <Button
            variant="outline"
            size="sm"
            onClick={addBookmark}
            title="Add Bookmark"
          >
            <FontAwesomeIcon icon={faBookmark} className="h-4 w-4" />
          </Button>
          <div className="relative">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSettingsMenu(!showSettingsMenu)}
              title="Browser Menu"
            >
              <FontAwesomeIcon icon={faBars} className="h-4 w-4" />
            </Button>

            {/* Chrome-style dropdown menu */}
            {showSettingsMenu && (
              <div
                ref={settingsMenuRef}
                className="absolute right-0 top-full mt-1 w-64 bg-popover border border-border rounded-md shadow-lg z-50"
              >
                <div className="py-1">
                  {/* Bookmarks Section */}
                  <div className="px-3 py-2 text-xs font-semibold text-muted-foreground border-b border-border">
                    Bookmarks
                  </div>
                  <button
                    onClick={() => {setShowBookmarks(!showBookmarks); setShowSettingsMenu(false);}}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-accent hover:text-accent-foreground flex items-center"
                  >
                    <FontAwesomeIcon icon={faBookmark} className="mr-3 h-4 w-4" />
                    {showBookmarks ? 'Hide Bookmarks' : 'Show Bookmarks'}
                  </button>

                  {/* Navigation Section */}
                  <div className="px-3 py-2 text-xs font-semibold text-muted-foreground border-b border-t border-border">
                    Navigation
                  </div>
                  <button
                    onClick={() => {goHome(); setShowSettingsMenu(false);}}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-accent hover:text-accent-foreground flex items-center"
                  >
                    <FontAwesomeIcon icon={faHome} className="mr-3 h-4 w-4" />
                    Home
                  </button>
                  <button
                    onClick={() => {refresh(); setShowSettingsMenu(false);}}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-accent hover:text-accent-foreground flex items-center"
                  >
                    <FontAwesomeIcon icon={faRefresh} className="mr-3 h-4 w-4" />
                    Reload
                  </button>

                  {/* Tools Section */}
                  <div className="px-3 py-2 text-xs font-semibold text-muted-foreground border-b border-t border-border">
                    Tools
                  </div>
                  <button
                    onClick={() => setShowSettingsMenu(false)}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-accent hover:text-accent-foreground flex items-center"
                  >
                    <FontAwesomeIcon icon={faDownload} className="mr-3 h-4 w-4" />
                    Downloads
                  </button>
                  <button
                    onClick={() => setShowSettingsMenu(false)}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-accent hover:text-accent-foreground flex items-center"
                  >
                    <FontAwesomeIcon icon={faHistory} className="mr-3 h-4 w-4" />
                    History
                  </button>
                  <button
                    onClick={() => {openInExternalBrowser(); setShowSettingsMenu(false);}}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-accent hover:text-accent-foreground flex items-center"
                  >
                    <FontAwesomeIcon icon={faExternalLinkAlt} className="mr-3 h-4 w-4" />
                    Open in External Browser
                  </button>

                  {/* Settings Section */}
                  <div className="px-3 py-2 text-xs font-semibold text-muted-foreground border-b border-t border-border">
                    Settings
                  </div>
                  <div className="px-3 py-2">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">JavaScript</span>
                        <input
                          type="checkbox"
                          checked={browserSettings.enableJavaScript}
                          onChange={(e) => setBrowserSettings(prev => ({...prev, enableJavaScript: e.target.checked}))}
                          className="rounded"
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Images</span>
                        <input
                          type="checkbox"
                          checked={browserSettings.enableImages}
                          onChange={(e) => setBrowserSettings(prev => ({...prev, enableImages: e.target.checked}))}
                          className="rounded"
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Cookies</span>
                        <input
                          type="checkbox"
                          checked={browserSettings.enableCookies}
                          onChange={(e) => setBrowserSettings(prev => ({...prev, enableCookies: e.target.checked}))}
                          className="rounded"
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Block Popups</span>
                        <input
                          type="checkbox"
                          checked={browserSettings.blockPopups}
                          onChange={(e) => setBrowserSettings(prev => ({...prev, blockPopups: e.target.checked}))}
                          className="rounded"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Advanced Settings */}
                  <div className="px-3 py-2 text-xs font-semibold text-muted-foreground border-b border-t border-border">
                    Advanced
                  </div>
                  <div className="px-3 py-2 space-y-2">
                    <div>
                      <label className="text-sm block mb-1">Zoom Level</label>
                      <select
                        value={browserSettings.zoomLevel}
                        onChange={(e) => setBrowserSettings(prev => ({...prev, zoomLevel: parseInt(e.target.value)}))}
                        className="w-full text-xs bg-background border rounded px-2 py-1"
                      >
                        <option value={50}>50%</option>
                        <option value={75}>75%</option>
                        <option value={100}>100%</option>
                        <option value={125}>125%</option>
                        <option value={150}>150%</option>
                      </select>
                    </div>
                    <div>
                      <label className="text-sm block mb-1">Homepage</label>
                      <input
                        type="text"
                        value={browserSettings.homepage}
                        onChange={(e) => setBrowserSettings(prev => ({...prev, homepage: e.target.value}))}
                        className="w-full text-xs bg-background border rounded px-2 py-1"
                        placeholder="https://www.google.com"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Bookmarks Bar */}
      {showBookmarks && bookmarks.length > 0 && (
        <div className="flex items-center p-2 bg-muted border-b border-border space-x-2 overflow-x-auto">
          {bookmarks.map((bookmark) => (
            <Button
              key={bookmark.id}
              variant="ghost"
              size="sm"
              onClick={() => navigateToBookmark(bookmark)}
              className="whitespace-nowrap"
            >
              {bookmark.title}
            </Button>
          ))}
        </div>
      )}

      {/* Browser Content */}
      <div className="flex-1 relative">
        {isLoading && (
          <div className="absolute inset-0 bg-background/80 flex items-center justify-center z-10">
            <div className="text-center">
              <FontAwesomeIcon icon={faRefresh} className="h-8 w-8 animate-spin text-primary mb-2" />
              <p className="text-sm text-muted-foreground">Loading...</p>
            </div>
          </div>
        )}

        {loadError ? (
          <div className="flex-1 flex items-center justify-center bg-background">
            <div className="text-center p-8">
              <FontAwesomeIcon icon={faShieldAlt} className="h-16 w-16 text-red-500 mb-4" />
              <h3 className="text-lg font-semibold mb-2">Site Cannot Be Displayed</h3>
              <p className="text-muted-foreground mb-4">
                This website blocks embedded browsing for security reasons.<br/>
                Most major sites (Google, Facebook, YouTube, etc.) prevent iframe embedding.
              </p>
              <div className="space-y-3">
                <div className="flex space-x-2">
                  <Button
                    onClick={() => {
                      setLoadError(false);
                      setIsLoading(true);
                      setTimeout(() => setIsLoading(false), 3000);
                    }}
                    variant="outline"
                    size="sm"
                  >
                    <FontAwesomeIcon icon={faRefresh} className="mr-2 h-4 w-4" />
                    Retry
                  </Button>
                  <Button
                    onClick={openInExternalBrowser}
                    variant="default"
                    size="sm"
                  >
                    <FontAwesomeIcon icon={faExternalLinkAlt} className="mr-2 h-4 w-4" />
                    Open Externally
                  </Button>
                </div>
                <div className="text-xs text-muted-foreground">
                  <div className="font-semibold mb-1">Sites that work in embedded browser:</div>
                  <div className="space-y-1">
                    <button
                      onClick={() => {setUrl('https://duckduckgo.com'); handleNavigate();}}
                      className="block text-blue-500 hover:underline"
                    >
                      • DuckDuckGo (Privacy-focused search)
                    </button>
                    <button
                      onClick={() => {setUrl('https://startpage.com'); handleNavigate();}}
                      className="block text-blue-500 hover:underline"
                    >
                      • Startpage (Private Google results)
                    </button>
                    <button
                      onClick={() => {setUrl('https://searx.org'); handleNavigate();}}
                      className="block text-blue-500 hover:underline"
                    >
                      • SearX (Open source search)
                    </button>
                    <button
                      onClick={() => {setUrl('https://wikipedia.org'); handleNavigate();}}
                      className="block text-blue-500 hover:underline"
                    >
                      • Wikipedia
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="w-full h-full relative">
            <iframe
              src={currentUrl}
              className="browser-iframe w-full h-full border-0"
              title="Browser Content"
              sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-downloads allow-top-navigation allow-modals"
              onLoad={(e) => {
                setIsLoading(false);
                setLoadError(false);
                // Check if iframe loaded successfully
                try {
                  const iframe = e.target;
                  // If we can't access the contentDocument, it might be blocked
                  if (!iframe.contentDocument && !iframe.contentWindow) {
                    setLoadError(true);
                  }
                } catch (error) {
                  // Cross-origin restrictions - this is expected for most sites
                  console.log('Cross-origin iframe detected (normal for external sites)');
                }
              }}
              onError={() => {
                setIsLoading(false);
                setLoadError(true);
              }}
              style={{
                transform: `scale(${browserSettings.zoomLevel / 100})`,
                transformOrigin: 'top left',
                width: `${100 / (browserSettings.zoomLevel / 100)}%`,
                height: `${100 / (browserSettings.zoomLevel / 100)}%`
              }}
            />

            {/* Overlay for iframe interaction issues */}
            <div
              className="absolute inset-0 pointer-events-none"
              style={{
                background: 'transparent',
                zIndex: loadError ? 10 : -1
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default Browser;