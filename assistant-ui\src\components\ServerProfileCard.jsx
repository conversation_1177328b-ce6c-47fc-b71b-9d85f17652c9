import React, { useState } from 'react';
import { 
  Server as ServerIcon, 
  Play as PlayIcon, 
  Square as StopIcon, 
  Settings as SettingsIcon, 
  CheckCircle as CheckCircleIcon, 
  XCircle as XCircleIcon, 
  AlertCircle as AlertCircleIcon,
  Star as StarIcon,
  Cpu as CpuIcon,
  HardDrive as HardDriveIcon,
  RefreshCw as RefreshCwIcon,
  Trash2 as TrashIcon,
  Edit as EditIcon
} from 'lucide-react';

const ServerProfileCard = ({ 
  profile, 
  isActive = false, 
  isRunning = false,
  onToggle,
  onSetActive,
  onDelete,
  onRefresh,
  className = ''
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const getStatusColor = () => {
    if (profile.verification_status === 'verified') return 'text-green-500';
    if (profile.verification_status === 'error') return 'text-red-500';
    return 'text-yellow-500';
  }

  const getStatusIcon = () => {
    if (profile.verification_status === 'verified') return <CheckCircleIcon className="w-4 h-4" />;
    if (profile.verification_status === 'error') return <XCircleIcon className="w-4 h-4" />;
    return <AlertCircleIcon className="w-4 h-4" />;
  }

  const getServerTypeIcon = () => {
    switch (profile.server_type) {
      case 'ollama':
        return <div className="w-6 h-6 bg-blue-500 rounded text-white text-xs flex items-center justify-center font-bold">O</div>;
      case 'llamacpp':
        return <div className="w-6 h-6 bg-purple-500 rounded text-white text-xs flex items-center justify-center font-bold">L</div>;
      case 'custom':
        return <div className="w-6 h-6 bg-gray-500 rounded text-white text-xs flex items-center justify-center font-bold">C</div>;
      default:
        return <ServerIcon className="w-6 h-6 text-gray-500" />;
    }
  }

  const handleRefresh = async () => {
    if (onRefresh) {
      setIsLoading(true);
      try {
        await onRefresh(profile.name);
      } finally {
        setIsLoading(false);
      }
    }
  }

  const formatLastVerified = (timestamp) => {
    if (!timestamp) return 'Never';
    const date = new Date(timestamp * 1000);
    return date.toLocaleString();
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          {getServerTypeIcon()}
          <div>
            <div className="flex items-center space-x-2">
              <h3 className="font-semibold text-gray-900 dark:text-white">{profile.name}</h3>
              {isActive && (
                <StarIcon className="w-4 h-4 text-yellow-500 fill-current" title="Active Server" />
              )}
              {isRunning && (
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" title="Running" />
              )}
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
              <span className={getStatusColor()}>
                {getStatusIcon()}
              </span>
              <span className="capitalize">{profile.server_type || 'Unknown'}</span>
              <span>•</span>
              <span className="capitalize">{profile.verification_status}</span>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Toggle Active */}
          {profile.enabled && (
            <button
              onClick={() => onSetActive && onSetActive(profile.name)}
              className={`px-3 py-1 rounded text-xs font-medium transition-colors ${ 
                isActive 
                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
              }`}
              title={isActive ? 'Currently Active' : 'Set as Active'}
            >
              {isActive ? 'Active' : 'Set Active'}
            </button>
          )}

          {/* Enable/Disable Toggle */}
          <label className="flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={profile.enabled}
              onChange={(e) => onToggle && onToggle(profile.name, e.target.checked)}
              className="sr-only"
            />
            <div className={`relative w-8 h-4 rounded-full transition-colors ${ 
              profile.enabled ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'
            }`}>
              <div className={`absolute top-0.5 left-0.5 w-3 h-3 bg-white rounded-full transition-transform ${ 
                profile.enabled ? 'translate-x-4' : ''
              }`} />
            </div>
          </label>

          {/* Actions Dropdown */}
          <div className="relative">
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
            >
              <SettingsIcon className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Path */}
      <div className="mb-3">
        <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Path</div>
        <div className="text-sm font-mono bg-gray-50 dark:bg-gray-700 p-2 rounded border text-gray-700 dark:text-gray-300 truncate">
          {profile.folder_path || 'Not configured'}
        </div>
      </div>

      {/* Executables Summary */}
      <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-3">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-1">
            <CpuIcon className="w-4 h-4" />
            <span>{profile.detected_executables?.length || 0} executables</span>
          </div>
          {profile.primary_executable && (
            <div className="flex items-center space-x-1">
              <HardDriveIcon className="w-4 h-4" />
              <span className="font-medium">{profile.primary_executable.file_name}</span>
            </div>
          )}
        </div>
        <div className="text-xs">
          Last verified: {formatLastVerified(profile.last_verified)}
        </div>
      </div>

      {/* Error Message */}
      {profile.error_message && (
        <div className="mb-3 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-sm text-red-700 dark:text-red-400">
          {profile.error_message}
        </div>
      )}

      {/* Detailed View */}
      {showDetails && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          {/* Actions */}
          <div className="flex space-x-2 mb-4">
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="flex items-center space-x-1 px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 text-sm"
            >
              <RefreshCwIcon className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
            <button
              onClick={() => onDelete && onDelete(profile.name)}
              className="flex items-center space-x-1 px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
            >
              <TrashIcon className="w-4 h-4" />
              <span>Delete</span>
            </button>
          </div>

          {/* Detected Executables */}
          {profile.detected_executables && profile.detected_executables.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Detected Executables</h4>
              <div className="space-y-2">
                {profile.detected_executables.map((exe, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">{exe.file_name}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{exe.path}</div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded">
                        {exe.executable_type}
                      </span>
                      <span className="text-xs text-gray-500">Priority: {exe.priority}</span>
                      {profile.primary_executable?.path === exe.path && (
                        <span className="text-xs px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 rounded">
                          Primary
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Metadata */}
          {profile.metadata && Object.keys(profile.metadata).length > 0 && (
            <div className="mt-4">
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Metadata</h4>
              <div className="grid grid-cols-2 gap-2">
                {Object.entries(profile.metadata).map(([key, value]) => (
                  <div key={key} className="text-xs">
                    <span className="text-gray-500 dark:text-gray-400">{key}:</span>
                    <span className="ml-1 text-gray-900 dark:text-white">{value}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ServerProfileCard;