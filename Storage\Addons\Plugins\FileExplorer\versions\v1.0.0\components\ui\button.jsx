import * as React from "react"

// Simplified button component without external dependencies
function Button({
  className = '',
  variant = 'default',
  size = 'default',
  asChild = false,
  ...props
}) {
  const baseClasses = "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 outline-none focus-visible:ring-2 focus-visible:ring-offset-2";
  
  const variantClasses = {
    default: "bg-blue-600 text-white shadow hover:bg-blue-700 focus-visible:ring-blue-500",
    destructive: "bg-red-600 text-white shadow hover:bg-red-700 focus-visible:ring-red-500",
    outline: "border border-gray-300 bg-white text-gray-700 shadow-sm hover:bg-gray-50 focus-visible:ring-blue-500",
    secondary: "bg-gray-100 text-gray-900 shadow-sm hover:bg-gray-200 focus-visible:ring-gray-500",
    ghost: "text-gray-700 hover:bg-gray-100 hover:text-gray-900",
    link: "text-blue-600 underline-offset-4 hover:underline",
  };
  
  const sizeClasses = {
    default: "h-9 px-4 py-2",
    sm: "h-8 px-3 text-xs",
    lg: "h-10 px-6",
    icon: "h-9 w-9 p-0",
  };
  
  const classes = `${baseClasses} ${variantClasses[variant] || variantClasses.default} ${sizeClasses[size] || sizeClasses.default} ${className}`;
  
  const Comp = asChild ? 'div' : 'button';
  
  return (
    <Comp
      className={classes}
      {...props}
    />
  );
}

export { Button }