use crate::ai_client::AiClient;

pub struct QueryEngine {
    ai_client: AiClient,
}

impl QueryEngine {
    pub fn new(ai_client: AiClient) -> Self {
        QueryEngine {
            ai_client,
        }
    }

    pub async fn send_prompt(&self, prompt: &str) -> Result<String, Box<dyn std::error::Error>> {
        let response = self.ai_client.generate_completion("qwen:0.5b", prompt).await?;
        Ok(response)
    }
}