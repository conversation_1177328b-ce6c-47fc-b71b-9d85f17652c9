# Dependencies
node_modules/
*/node_modules/

# Build outputs
dist/
build/
target/
*.exe
*.app
*.dmg
*.deb

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db
desktop.ini

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Cache directories
.npm
.cache/
.parcel-cache/

# Temporary folders
tmp/
temp/

# SQLite databases (if any)
*.db
*.sqlite
*.sqlite3

# Rust specific
Cargo.lock
target/

# Tauri specific
src-tauri/target/
src-tauri/Cargo.lock

# Storage directory (contains user data, models, plugins)
# Consider if you want to include this or not
Storage/System/User/
Storage/System/Models/
Storage/System/logs/
Storage/.system_index/