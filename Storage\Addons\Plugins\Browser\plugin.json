{"name": "Browser", "description": "A lightweight, comprehensive browser for The Collective with tab management, bookmarks, and history tracking.", "version": "1.0.0", "app_type": "app", "enabled": true, "main": "browser.jsx", "class_name": "Browser", "plugin_type": "ui_component", "author": "The Collective Team", "permissions": ["web_access", "local_storage", "file_system"], "data_directory": "./data/", "versions": [{"version": "1.0.0", "path": "./versions/v1.0.0/", "changelog": "Full-featured browser with tab management and modular architecture"}], "features": ["Multi-tab browsing", "Bookmarks management", "Browsing history", "Download manager", "Navigation controls", "Embedded iframe browsing", "Zoom controls", "Settings management"], "settings": {"default_homepage": "https://www.google.com", "enable_bookmarks": true, "enable_history": true, "max_history_entries": 1000, "enable_downloads": true, "download_directory": "./data/downloads/", "enable_multiple_tabs": true, "max_tabs": 10, "default_zoom": 100, "block_popups": true, "enable_javascript": true}}