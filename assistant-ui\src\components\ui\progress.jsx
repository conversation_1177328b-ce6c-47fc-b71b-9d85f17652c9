import React from 'react';

const Progress = ({ value = 0, className = '', ...props }) => {
  // Ensure value is between 0 and 100
  const normalizedValue = Math.min(100, Math.max(0, value));

  return (
    <div
      className={`relative h-2 w-full overflow-hidden rounded-full bg-gray-200 dark:bg-gray-800 ${className}`}
      {...props}
    >
      <div
        className="h-full bg-blue-600 transition-all duration-300 ease-in-out dark:bg-blue-400"
        style={{
          width: `${normalizedValue}%`,
        }}
      />
    </div>
  );
};

export { Progress };
