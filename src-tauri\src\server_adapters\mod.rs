use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use crate::settings_manager::ServerProfile;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerResponse {
    pub content: String,
    pub model: String,
    pub done: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StreamChunk {
    pub content: String,
    pub done: bool,
}

#[derive(Debug)]
pub enum AdapterError {
    Reqwest(reqwest::Error),
    <PERSON><PERSON>(serde_json::Error),
    Custom(String),
    ServerNotRunning,
    InvalidProfile,
}

impl From<reqwest::Error> for AdapterError {
    fn from(err: reqwest::Error) -> Self {
        AdapterError::Reqwest(err)
    }
}

impl From<serde_json::Error> for AdapterError {
    fn from(err: serde_json::Error) -> Self {
        AdapterError::<PERSON><PERSON>(err)
    }
}

impl std::fmt::Display for AdapterError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            AdapterError::Reqwest(err) => write!(f, "Request error: {}", err),
            AdapterError::Json(err) => write!(f, "JSON error: {}", err),
            AdapterError::Custom(err) => write!(f, "{}", err),
            AdapterError::ServerNotRunning => write!(f, "Server is not running"),
            AdapterError::InvalidProfile => write!(f, "Invalid server profile"),
        }
    }
}

/// Trait that all server adapters must implement for modular server support
#[async_trait::async_trait]
pub trait ServerAdapter: Send + Sync {
    /// Get the base URL for this server adapter
    fn get_base_url(&self) -> String;
    
    /// Check if the server is currently running and responding
    async fn is_running(&self) -> bool;
    
    /// Get list of available models from the server
    async fn get_models(&self) -> Result<Vec<String>, AdapterError>;
    
    /// Send a chat message and return streaming response
    async fn send_message(&self, model: &str, prompt: &str) -> Result<reqwest::Response, AdapterError>;
    
    /// Generate a welcome message using the specified model
    async fn generate_welcome_message(&self, model: &str) -> Result<String, AdapterError>;
    
    /// Get server-specific health check endpoint
    fn get_health_endpoint(&self) -> String;
    
    /// Get server type identifier
    fn get_server_type(&self) -> &'static str;
    
    /// Validate that this adapter can work with the given server profile
    fn validate_profile(&self, profile: &ServerProfile) -> Result<(), AdapterError>;
}

pub mod ollama;
pub mod llamacpp;
pub mod custom;

pub use ollama::OllamaAdapter;
pub use llamacpp::LlamaCppAdapter;
pub use custom::CustomAdapter;

/// Factory function to create the appropriate adapter based on server type
pub fn create_adapter_for_profile(profile: &ServerProfile, base_url: String) -> Result<Box<dyn ServerAdapter>, AdapterError> {
    match profile.server_type.as_deref() {
        Some("ollama") => {
            let adapter = OllamaAdapter::new(base_url);
            adapter.validate_profile(profile)?;
            Ok(Box::new(adapter))
        },
        Some("llamacpp") => {
            let adapter = LlamaCppAdapter::new(base_url);
            adapter.validate_profile(profile)?;
            Ok(Box::new(adapter))
        },
        Some("custom") => {
            let adapter = CustomAdapter::new(base_url, HashMap::new());
            adapter.validate_profile(profile)?;
            Ok(Box::new(adapter))
        },
        _ => {
            // Default fallback to Ollama for unknown types
            let adapter = OllamaAdapter::new(base_url);
            Ok(Box::new(adapter))
        }
    }
}

/// Create adapter from active server profile
pub async fn create_adapter_from_active_profile() -> Result<Box<dyn ServerAdapter>, AdapterError> {
    use crate::settings_manager::get_active_server_profile;
    
    let active_profile = get_active_server_profile().await
        .map_err(|e| AdapterError::Custom(format!("Failed to get active server profile: {}", e)))?;
    
    if let Some(profile) = active_profile {
        let base_url = "http://127.0.0.1:11435".to_string(); // Default base URL
        
        create_adapter_for_profile(&profile, base_url)
    } else {
        Err(AdapterError::Custom("No active server profile configured".to_string()))
    }
}