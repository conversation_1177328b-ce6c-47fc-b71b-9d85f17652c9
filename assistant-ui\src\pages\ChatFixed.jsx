import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useAi } from '../hooks/useAi.js';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faPaperPlane, 
  faRobot, 
  faUser, 
  faSpinner,
  faRefresh
} from '@fortawesome/free-solid-svg-icons';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';

const ChatFixed = () => {
  // State
  const {
    availableModels,
    selectedModel,
    setSelectedModel,
    isLoadingModels,
    isPreloading,
    modelError,
    preloadingError,
    fetchAvailableModels: refreshModels,
    preloadModel,
  } = useAi();
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  // Refs
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  const messagesContainerRef = useRef(null);
  const isMounted = useRef(true);
  const scrollAnimationFrame = useRef(null);

  // Listener for streaming chat responses
  useEffect(() => {
    isMounted.current = true;

    const unlistenToken = listen('chat_token', (event) => {
      const { content } = event.payload;
      setMessages(prev => {
        const newMessages = [...prev];
        const lastMessage = newMessages[newMessages.length - 1];
        if (lastMessage && lastMessage.sender === 'assistant') {
          lastMessage.content += content;
          lastMessage.isTyping = false; // Stop typing animation once tokens arrive
        }
        return newMessages;
      });
    });

    const unlistenEnd = listen('chat_token_end', () => {
      setIsLoading(false);
    });

    const unlistenError = listen('chat_error', (event) => {
      const errorMessage = event.payload;
      addMessage({
        id: `err_${Date.now()}`,
        content: `Error: ${errorMessage}`,
        sender: 'system',
        isError: true,
      });
      setIsLoading(false);
    });

    return () => {
      isMounted.current = false;
      if (scrollAnimationFrame.current) {
        cancelAnimationFrame(scrollAnimationFrame.current);
      }
      unlistenToken.then(f => f());
      unlistenEnd.then(f => f());
      unlistenError.then(f => f());
    };
  }, []);

  // Generate AI welcome message
  const generateWelcomeMessage = useCallback(async () => {
    if (!selectedModel || messages.length > 0 || isPreloading || preloadingError) return;
    
    try {
      const tempId = `temp_${Date.now()}`;
      addMessage({
        id: tempId,
        content: '...',
        sender: 'assistant',
        timestamp: new Date(),
        isTyping: true
      });
      
      const welcomeMessage = await invoke('generate_welcome_message_command', { modelName: selectedModel });
      
      if (!isMounted.current) return;
      
      setMessages(prev => [
        ...prev.filter(msg => msg.id !== tempId),
        {
          id: `welcome_${Date.now()}`,
          content: welcomeMessage,
          sender: 'assistant',
          timestamp: new Date(),
          isWelcome: true
        }
      ]);
    } catch (error) {
      console.error('Failed to generate welcome message:', error);
      if (isMounted.current) {
        setMessages(prev => [
          ...prev.filter(msg => !msg.isTyping),
          {
            id: `welcome_${Date.now()}`,
            content: "Hello! I'm here to help. How can I assist you today?",
            sender: 'assistant',
            timestamp: new Date(),
            isWelcome: true
          }
        ]);
      }
    }
  }, [selectedModel, messages.length, isPreloading, preloadingError]);

  // Initialize chat
  useEffect(() => {    
    if (isMounted.current && messages.length === 0 && selectedModel && !isPreloading && !preloadingError) {
      generateWelcomeMessage();
    }
  }, [selectedModel, isPreloading, preloadingError, generateWelcomeMessage]);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (!isMounted.current) return;
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = useCallback(() => {
    if (!isMounted.current || !messagesEndRef.current) return;
    try {
      if (scrollAnimationFrame.current) {
        cancelAnimationFrame(scrollAnimationFrame.current);
      }
      scrollAnimationFrame.current = requestAnimationFrame(() => {
        try {
          if (isMounted.current && messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'start' });
          }
        } catch (error) {
          console.warn('Error during scroll:', error);
        }
      });
    } catch (error) {
      console.warn('Error in scrollToBottom:', error);
    }
  }, []);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    const trimmedInput = input.trim();
    if (!trimmedInput || isLoading || isPreloading || !isMounted.current) return;

    if (!selectedModel) {
      addMessage({ content: 'Please select a model first.', sender: 'system', isError: true });
      return;
    }

    const userMessage = {
      id: `msg_${Date.now()}`,
      content: trimmedInput,
      sender: 'user',
      timestamp: new Date(),
      model_used: selectedModel
    };

    const assistantMessage = {
        id: `msg_${Date.now() + 1}`,
        content: '',
        sender: 'assistant',
        timestamp: new Date(),
        model_used: selectedModel,
        isTyping: true, // Start with typing indicator
    };

    setMessages(prev => [...prev, userMessage, assistantMessage]);
    setInput('');
    setIsLoading(true);

    try {
      await invoke('send_chat_message_command', {
        modelName: selectedModel,
        prompt: trimmedInput
      });
    } catch (error) {
      console.error('Error sending message:', error);
      if (isMounted.current) {
        addMessage({ content: `Error: ${error}`, sender: 'system', isError: true });
        setIsLoading(false);
      }
    }
  };

  const addMessage = (message) => {
    if (!isMounted.current) return;
    setMessages(prev => [...prev, { ...message, id: message.id || `msg_${Date.now()}` }]);
  };

  const renderMessage = (message) => {
    if (!message || !message.id) return null;
    
    return (
      <div 
        key={message.id}
        className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'} mb-4`}
      >
        <div 
          className={`max-w-[80%] rounded-lg p-3 ${
            message.sender === 'user' 
              ? 'bg-primary text-primary-foreground' 
              : message.isError 
                ? 'bg-destructive/10 text-destructive border border-destructive/20'
                : 'bg-muted'
          }`}
        >
          {message.isTyping ? (
            <div className="flex space-x-1">
              <div className="w-2 h-2 rounded-full bg-muted-foreground animate-bounce" style={{ animationDelay: '0ms' }} />
              <div className="w-2 h-2 rounded-full bg-muted-foreground animate-bounce" style={{ animationDelay: '150ms' }} />
              <div className="w-2 h-2 rounded-full bg-muted-foreground animate-bounce" style={{ animationDelay: '300ms' }} />
            </div>
          ) : (
            <div className="whitespace-pre-wrap">{message.content}</div>
          )}
          <div className="text-xs opacity-70 mt-1">
            {message.timestamp && new Date(message.timestamp).toLocaleTimeString()}
            {message.model_used && ` • ${message.model_used}`}
          </div>
        </div>
      </div>
    );
  };

  if (!isMounted.current) {
    return null;
  }

  return (
    <div className="container mx-auto p-4 h-full flex flex-col">
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto mb-4 space-y-4 p-2"
      >
        {messages.length === 0 && isPreloading && (
          <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
            <FontAwesomeIcon icon={faSpinner} className="h-8 w-8 animate-spin text-primary mb-4" />
            <h3 className="text-xl font-semibold text-foreground mb-2">
              Warming up the model...
            </h3>
            <p className="text-muted-foreground max-w-md text-center">
              This may take a moment.
            </p>
          </div>
        )}
        {messages.length === 0 && preloadingError && (
          <div className="flex flex-col items-center justify-center h-full text-destructive">
            <h3 className="text-xl font-semibold mb-2">Error</h3>
            <p className="max-w-md text-center">{preloadingError}</p>
          </div>
        )}
        {messages.length === 0 && !isPreloading && !preloadingError && (
          <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
              <FontAwesomeIcon icon={faRobot} className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold text-foreground mb-2">
              Start a conversation
            </h3>
            <p className="text-muted-foreground max-w-md text-center">
              Ask me anything! I'm powered by {selectedModel || 'AI'} and ready to help.
            </p>
          </div>
        )}
        {messages.map(renderMessage)}
        <div ref={messagesEndRef} className="h-px" />
      </div>

      <div className="border-t border-border bg-card p-4">
        <form onSubmit={handleSubmit} className="space-y-3">
          <div className="flex items-center gap-2">
            <div className="relative flex-1">
              <Select 
                value={selectedModel} 
                onValueChange={(model) => {
                  setSelectedModel(model);
                  preloadModel(model);
                }}
                disabled={isLoadingModels || !!modelError || isLoading || isPreloading}
              >
                <SelectTrigger className="w-full">
                  <div className="flex items-center gap-2">
                    {isLoadingModels && !availableModels.length ? (
                      <span className="flex items-center gap-2">
                        <FontAwesomeIcon icon={faSpinner} className="h-4 w-4 animate-spin" />
                        <span>Loading models...</span>
                      </span>
                    ) : modelError ? (
                      <span className="text-red-500">Error loading models</span>
                    ) : (
                      <>
                        <span>{selectedModel || 'Select a model'}</span>
                        {isPreloading && <FontAwesomeIcon icon={faSpinner} className="h-4 w-4 animate-spin text-muted-foreground" title="Preloading model..." />}
                      </>
                    )}
                  </div>
                </SelectTrigger>
                <SelectContent>
                  {availableModels.map((model) => (
                    <SelectItem key={model} value={model}>
                      {model}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {modelError && (
                <p className="mt-1 text-xs text-red-500">{modelError}</p>
              )}
              {preloadingError && (
                <p className="mt-1 text-xs text-red-500">{preloadingError}</p>
              )}
            </div>
            <Button 
              variant="outline" 
              size="icon" 
              onClick={refreshModels}
              disabled={isLoadingModels || isLoading || isPreloading}
              type="button"
            >
              <FontAwesomeIcon 
                icon={isLoadingModels ? faSpinner : faRefresh} 
                className={`h-4 w-4 ${isLoadingModels ? 'animate-spin' : ''}`} 
              />
            </Button>
          </div>
          
          <div className="flex gap-2">
            <Input
              ref={inputRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="Type your message..."
              disabled={isLoading || isPreloading || !selectedModel}
              className="flex-1"
            />
            <Button 
              type="submit" 
              disabled={!input.trim() || isLoading || isPreloading || !selectedModel}
            >
              <FontAwesomeIcon icon={isLoading ? faSpinner : faPaperPlane} className="h-4 w-4 mr-2" />
              {isLoading ? 'Sending...' : 'Send'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ChatFixed;