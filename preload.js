// Preload script for Electron
const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Send messages to main process
  sendMessage: (channel, data) => {
    // Whitelist channels
    let validChannels = ['restart-backend', 'query-assistant', 'drag-drop-files'];
    if (validChannels.includes(channel)) {
      ipcRenderer.send(channel, data);
    }
  },
  // Receive messages from main process
  receiveMessage: (channel, func) => {
    let validChannels = ['assistant-response', 'backend-status', 'file-indexed'];
    if (validChannels.includes(channel)) {
      // Deliberately strip event as it includes `sender` 
      ipcRenderer.on(channel, (event, ...args) => func(...args));
    }
  },
  openDialog: (type) => ipcRenderer.invoke('dialog_open', { dialog_type: type }),
  getBackendPort: () => ipcRenderer.invoke('get-backend-port') // New IPC handler
});