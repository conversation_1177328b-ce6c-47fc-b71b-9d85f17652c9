Dashboard-Home
-Xbox style 
-live tiles, various sizes
•Workshop section(showing all running workshops) 
•chat box
•recent apps 



Chat app(core)
-infinite cascading chat, no need to close and reset, or start new, 
-extract valuable info and context it, preferences, ideas/plans, 
-task creation,note creation, 
-multiple phases(normal chat,development mode 
-Topics
-we have a mobile chat that should also be intergrated in our chat app it pops up everywhere else, the page 


Workshop app(core)
Visual representation-
•2d ai agents 
•status bar with %
•when clicked on show all tasks and completion rates, 
-Simulate/test 
-launch to our AppStore 

Processor app(core)
-The computing aspect this mode simple allows other devices to process using their cpus mostly for distibutued computing 
-along with system status this will allows us to use multiple devices in tandem 
-I want all processes to be ran internally(on each device) so they can be slowed paused reviewed 
- 


Runtime mode-initilizes at startup always running core functions in the background, tool mode-ai uses it without opening it, app mode-fully opens the app



Initialization
-entire process describes in general settings
-on start apps 
-runtimes begin


Devices new entry in settings
-Connected devices(physically, cloud, app-other device)
-Controllers



Profiles
-beautiful cards/smart folders 
-deep coding capabilities, they can be as simple as a social media profile with username and password but also tho busy enough to add coded settings for example browser cache/cookies and history 
-another example our api section, those should be displayed as cards and coded as api key Profiles
-servers are the perfect example also all those files in a folder yet we know eacttly the runtime to launch without looking
-we need a robust profile system, with multiple templates, our current apps cards are nice i think they need a big overhaul, our logic section has good profile cards too so that should be a template sytle

System status(core Tool)
-Homeostasis
 •device sense(name, gpu, cpu, temp,battery or charged/pluggedin)
 •In app process 
 •external processes
 •network usage 
 •slow down or speed up processes depending on status


Void app(core tool)
-the void is our equivalent to a digital crystal ball 
-any and all files can be rendered in the void, when we chat with ai and it generates an image a void window will be the thing to show it, the void gives us a digital display, it’s deprecate from our file explorer in that it’s  a gateway display 
-json txt ect ect it renders it, 
-allow for inspect/summarize in which a void window pops up 
-the void will become a full fledge Vr environment 
-void viewer will view things running in tool mode and allow to expand which opens a window to full app mode



Circuit 
-show all linked between apps tools and code
-n8n style preferably a big circuit display of plugged in lanes to our central system
-the circuit is out stream of consciousness 


Vault 
-located in settings next to System, Addons tabs
-all storage for our operations 
-folders such as Downloads, Assets, RAG, and others
-I think I file explorer should be the one one to open it 


System log(in settings)
-a log list of everything and every app and its operations, starting initialization 


Memory and saves
-each app should save its own data 
-our systems itself saves everything path related in user preferences 


Appearance, 
-change the name theme to light/dark mode
-Create an actual theme selector
-use shadcn ux ui design mcp to plan out the perfect ux ui redesign for our entire app and all its current features 
-these Theme must be reusable 
-save to path to vault/assets/


Custom extensions
In our settings create a new section called extensions maybe under what’s currently docket, list out all extensions and assoiated file type


General ideas
-our systems is what we’ll consider to be co-op, human and ai interactive
-everything must have visual queues and be visually represented, 
-each device running the app should have a cast mode, 
-upgrade our tools page to look more like an appstore, also our apps should have a display page when clicked from tools page shoing general info and versions kind of like an actual appstore, those files hsould be stored in app folders also, to remain modular
-clean up all redundant code especially in file viewer 
-fix our current apps browser explorer ect ect theyre hideous and not very common sense ux ui wise 
