use async_trait::async_trait;
use reqwest::Client;
use serde_json;
use crate::settings_manager::ServerProfile;
use super::{ServerAdapter, AdapterError, ServerResponse};

pub struct LlamaCppAdapter {
    client: Client,
    base_url: String,
}

impl LlamaCppAdapter {
    pub fn new(base_url: String) -> Self {
        Self {
            client: Client::new(),
            base_url,
        }
    }
}

#[async_trait]
impl ServerAdapter for LlamaCppAdapter {
    fn get_base_url(&self) -> String {
        self.base_url.clone()
    }
    
    async fn is_running(&self) -> bool {
        let health_url = format!("{}/health", self.base_url);
        match self.client.get(&health_url).send().await {
            Ok(response) => {
                if response.status().is_success() {
                    if let Ok(json) = response.json::<serde_json::Value>().await {
                        return json.get("status").and_then(|s| s.as_str()) == Some("ok");
                    }
                }
            },
            Err(_) => {}
        }
        
        // Fallback check - try to access the completion endpoint
        let completion_url = format!("{}/completion", self.base_url);
        match self.client.get(&completion_url).send().await {
            Ok(response) => response.status() == 405 || response.status().is_success(), // 405 Method Not Allowed is expected for GET on completion
            Err(_) => false,
        }
    }
    
    async fn get_models(&self) -> Result<Vec<String>, AdapterError> {
        // llama.cpp server doesn't have a models endpoint
        // Return the model name from the loaded model (hardcoded for now, but should be configurable)
        use crate::settings_manager::UserPreferences;
        let prefs = UserPreferences::load();
        let models_path = std::path::Path::new(&prefs.models_path);
        
        let mut models = Vec::new();
        
        // Look for .gguf files in the models directory
        if models_path.exists() && models_path.is_dir() {
            if let Ok(entries) = std::fs::read_dir(models_path) {
                for entry in entries {
                    if let Ok(entry) = entry {
                        let path = entry.path();
                        if path.extension().and_then(|s| s.to_str()) == Some("gguf") {
                            if let Some(stem) = path.file_stem().and_then(|s| s.to_str()) {
                                models.push(stem.to_string());
                            }
                        }
                    }
                }
            }
        }
        
        // Fallback if no models found
        if models.is_empty() {
            models.push("qwen3-0.6b".to_string());
        }
        
        Ok(models)
    }
    
    async fn send_message(&self, _model: &str, prompt: &str) -> Result<reqwest::Response, AdapterError> {
        let url = format!("{}/completion", self.base_url);
        let body = serde_json::json!({
            "prompt": prompt,
            "n_predict": 256,
            "stream": true,
            "temperature": 0.3,
            "top_p": 0.8,
            "top_k": 20,
            "repeat_penalty": 1.05,
        });
        
        let response = self.client.post(&url).json(&body).send().await?;
        
        if !response.status().is_success() {
            return Err(AdapterError::Custom(format!("Server returned status: {}", response.status())));
        }
        
        Ok(response)
    }
    
    async fn generate_welcome_message(&self, _model: &str) -> Result<String, AdapterError> {
        let prompt = "As a friendly and helpful AI assistant, introduce yourself to the user in a single, welcoming sentence.";
        let url = format!("{}/completion", self.base_url);
        let body = serde_json::json!({
            "prompt": prompt,
            "n_predict": 50,
            "stream": false
        });
        
        let response = self.client.post(&url).json(&body).send().await?;
        
        if !response.status().is_success() {
            return Err(AdapterError::Custom(format!("Server returned status: {}", response.status())));
        }
        
        let json: serde_json::Value = response.json().await?;
        let content = json.get("content")
            .and_then(|r| r.as_str())
            .unwrap_or("Hello! How can I help you today?")
            .trim()
            .to_string();
        
        Ok(content)
    }
    
    fn get_health_endpoint(&self) -> String {
        format!("{}/health", self.base_url)
    }
    
    fn get_server_type(&self) -> &'static str {
        "llamacpp"
    }
    
    fn validate_profile(&self, profile: &ServerProfile) -> Result<(), AdapterError> {
        if profile.server_type.as_deref() != Some("llamacpp") {
            return Err(AdapterError::InvalidProfile);
        }
        
        // More lenient validation - if the profile is enabled and has a folder path, accept it
        // The server might already be running even if we can't detect the exact executable
        if !profile.enabled {
            return Err(AdapterError::Custom("llama.cpp server profile is disabled".to_string()));
        }
        
        // Check for llama-server executable (optional check)
        let has_llama_server = profile.detected_executables.iter()
            .any(|exe| exe.file_name.to_lowercase().contains("llama-server") || exe.file_name.to_lowercase().contains("llama"));
        
        if !has_llama_server && !profile.detected_executables.is_empty() {
            println!("Warning: No llama-server executable detected, but profile has other executables");
        }
        
        Ok(())
    }
}