import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { Button } from './ui/button';
import { Switch } from './ui/switch';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faServer, faPowerOff, faSync, faCheckCircle, faExclamationCircle } from '@fortawesome/free-solid-svg-icons';

const ServerStatus = () => {
  const [serverStatus, setServerStatus] = useState('stopped');
  const [models, setModels] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const checkServerStatus = async () => {
    try {
      const status = await invoke('get_server_status');
      setServerStatus(status.status);
    } catch (error) {
      console.error('Failed to get server status:', error);
      setServerStatus('error');
    }
  };

  const fetchModels = async () => {
    try {
      const modelList = await invoke('get_models_command');
      setModels(modelList);
    } catch (error) {
      console.error('Failed to fetch models:', error);
      setModels([]);
    }
  };

  useEffect(() => {
    checkServerStatus();
    const interval = setInterval(checkServerStatus, 5000); // Check every 5 seconds
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (serverStatus === 'running') {
      fetchModels();
    }
  }, [serverStatus]);

  const handleToggleServer = async () => {
    setIsLoading(true);
    try {
      if (serverStatus === 'running') {
        await invoke('stop_server_command');
      } else {
        await invoke('start_server_command');
      }
      await checkServerStatus();
    } catch (error) {
      console.error('Failed to toggle server:', error);
    }
    setIsLoading(false);
  };

  const handleRefreshModels = async () => {
    setIsLoading(true);
    await fetchModels();
    setIsLoading(false);
  };

  const getStatusColor = () => {
    switch (serverStatus) {
      case 'running':
        return 'text-green-500';
      case 'stopped':
        return 'text-red-500';
      default:
        return 'text-yellow-500';
    }
  };

  return (
    <div className="p-4 border rounded-lg">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <FontAwesomeIcon icon={faServer} className={`h-6 w-6 ${getStatusColor()}`} />
          <div>
            <h3 className="font-semibold">AI Server</h3>
            <p className={`text-sm ${getStatusColor()}`}>{serverStatus}</p>
          </div>
        </div>
        <Switch
          checked={serverStatus === 'running'}
          onCheckedChange={handleToggleServer}
          disabled={isLoading}
        />
      </div>
      {serverStatus === 'running' && (
        <div className="mt-4">
          <div className="flex items-center justify-between">
            <h4 className="font-semibold">Models ({models.length})</h4>
            <Button variant="ghost" size="sm" onClick={handleRefreshModels} disabled={isLoading}>
              <FontAwesomeIcon icon={faSync} className={isLoading ? 'animate-spin' : ''} />
            </Button>
          </div>
          <ul className="mt-2 space-y-1 text-sm">
            {models.map((model) => (
              <li key={model} className="flex items-center space-x-2">
                <FontAwesomeIcon icon={faCheckCircle} className="text-green-500" />
                <span>{model}</span>
              </li>
            ))}
            {models.length === 0 && (
              <li className="flex items-center space-x-2 text-muted-foreground">
                <FontAwesomeIcon icon={faExclamationCircle} className="text-yellow-500" />
                <span>No models found.</span>
              </li>
            )}
          </ul>
        </div>
      )}
    </div>
  );
};

export default ServerStatus;
