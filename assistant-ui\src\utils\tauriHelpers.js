/**
 * <PERSON>ri Helpers
 * 
 * This module provides safe wrappers around Tauri APIs to handle cases where
 * Tauri might not be available (e.g., when running in a browser).
 */

let _isTauri = null;
let _tauriApis = {};

/**
 * Check if running in Tauri environment
 * @returns {Promise<boolean>} True if running in Tauri
 */
export const isTauri = async () => {
  if (_isTauri === null) {
    try {
      _isTauri = window.__TAURI__ !== undefined;
      
      // If we're in Tauri, cache the APIs we'll need
      if (_isTauri) {
        try {
          // In Tauri v2, we can use the global window.__TAURI__ object
          if (window.__TAURI__) {
            const { invoke } = window.__TAURI__.core;
            const { open, save } = window.__TAURI__.dialog;
            const { readTextFile, writeTextFile, exists } = window.__TAURI__.fs;
            const { join, appDataDir } = window.__TAURI__.path;
            const { WebviewWindow } = window.__TAURI__.window;
            const { Command } = window.__TAURI__.shell;
            
            _tauriApis = {
              invoke,
              dialog: { open, save },
              fs: { readTextFile, writeTextFile, exists },
              path: { join, appDataDir },
              window: { WebviewWindow },
              shell: { Command },
            };
          }
        } catch (e) {
          console.warn('Error initializing Tauri APIs:', e);
          _isTauri = false;
        }
      }
    } catch (e) {
      console.warn('Error loading Tauri APIs:', e);
      _isTauri = false;
    }
  }
  return _isTauri;
};

/**
 * Safe wrapper around Tauri's invoke
 * @param {string} command - The command to invoke
 * @param {object} [args={}] - Arguments to pass to the command
 * @returns {Promise<any>} The result of the command
 */
export const safeInvoke = async (command, args = {}) => {
  try {
    const isTauriEnv = await isTauri();
    if (!isTauriEnv) {
      console.warn(`Tauri not available - skipping invoke: ${command}`, args);
      throw new Error('Tauri backend not available');
    }

    if (!_tauriApis.invoke) {
      throw new Error('Tauri invoke API not available');
    }

    // Add timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error(`Tauri invoke timeout: ${command}`)), 10000)
    );

    return await Promise.race([
      _tauriApis.invoke(command, args),
      timeoutPromise
    ]);
  } catch (error) {
    console.error(`Error in Tauri invoke (${command}):`, error);
    throw error; // Re-throw to allow callers to handle the error
  }
};

/**
 * Get a Tauri API module
 * @param {string} moduleName - Name of the Tauri API module (e.g., 'dialog', 'fs')
 * @returns {object|null} The Tauri API module or null if not available
 */
export const getTauriModule = async (moduleName) => {
  if (!(await isTauri())) {
    console.warn(`Tauri not available - cannot get module: ${moduleName}`);
    return null;
  }
  
  if (!_tauriApis[moduleName]) {
    console.warn(`Tauri module not loaded: ${moduleName}`);
    return null;
  }
  
  return _tauriApis[moduleName];
};

/**
 * Check if a file exists (works in both Tauri and browser environments)
 * @param {string} path - Path to check
 * @returns {Promise<boolean>} True if the file exists
 */
export const fileExists = async (path) => {
  if (await isTauri()) {
    try {
      const fs = await getTauriModule('fs');
      if (!fs) return false;
      
      const { exists } = fs;
      return await exists(path);
    } catch (e) {
      console.error('Error checking if file exists:', e);
      return false;
    }
  }
  
  // Fallback for browser environment
  try {
    const response = await fetch(path, { method: 'HEAD' });
    return response.ok;
  } catch (e) {
    return false;
  }
};

export default {
  isTauri,
  safeInvoke,
  getTauriModule,
  fileExists,
};
