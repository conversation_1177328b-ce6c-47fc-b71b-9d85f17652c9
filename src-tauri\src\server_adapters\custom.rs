use async_trait::async_trait;
use reqwest::Client;
use serde_json;
use std::collections::HashMap;
use crate::settings_manager::ServerProfile;
use super::{ServerAdapter, AdapterError, ServerResponse};

pub struct CustomAdapter {
    client: Client,
    base_url: String,
    config: HashMap<String, serde_json::Value>,
}

impl CustomAdapter {
    pub fn new(base_url: String, config: HashMap<String, serde_json::Value>) -> Self {
        Self {
            client: Client::new(),
            base_url,
            config,
        }
    }
    
    fn get_endpoint(&self, endpoint_type: &str) -> String {
        let endpoint = self.config.get(&format!("{}_endpoint", endpoint_type))
            .and_then(|v| v.as_str())
            .unwrap_or_else(|| {
                match endpoint_type {
                    "health" => "/health",
                    "models" => "/api/models",
                    "chat" => "/api/chat",
                    "generate" => "/api/generate",
                    _ => "/api/unknown"
                }
            });
        
        format!("{}{}", self.base_url, endpoint)
    }
    
    fn get_request_template(&self, request_type: &str) -> serde_json::Value {
        self.config.get(&format!("{}_template", request_type))
            .cloned()
            .unwrap_or_else(|| {
                match request_type {
                    "chat" => serde_json::json!({
                        "prompt": "{prompt}",
                        "stream": true
                    }),
                    "welcome" => serde_json::json!({
                        "prompt": "{prompt}",
                        "stream": false
                    }),
                    _ => serde_json::json!({})
                }
            })
    }
}

#[async_trait]
impl ServerAdapter for CustomAdapter {
    fn get_base_url(&self) -> String {
        self.base_url.clone()
    }
    
    async fn is_running(&self) -> bool {
        let health_url = self.get_endpoint("health");
        match self.client.get(&health_url).send().await {
            Ok(response) => response.status().is_success(),
            Err(_) => false,
        }
    }
    
    async fn get_models(&self) -> Result<Vec<String>, AdapterError> {
        let models_url = self.get_endpoint("models");
        let response = self.client.get(&models_url).send().await?;
        
        if !response.status().is_success() {
            return Err(AdapterError::Custom(format!("Server returned status: {}", response.status())));
        }
        
        let json: serde_json::Value = response.json().await?;
        
        // Try different common response formats
        let models: Vec<String> = if let Some(models_array) = json.get("models").and_then(|m| m.as_array()) {
            // Ollama-style: {"models": [{"name": "model1"}, {"name": "model2"}]}
            models_array.iter()
                .filter_map(|m| m.get("name").and_then(|n| n.as_str()).map(|s| s.to_string()))
                .collect()
        } else if let Some(data_array) = json.get("data").and_then(|d| d.as_array()) {
            // OpenAI-style: {"data": [{"id": "model1"}, {"id": "model2"}]}
            data_array.iter()
                .filter_map(|m| m.get("id").and_then(|n| n.as_str()).map(|s| s.to_string()))
                .collect()
        } else if let Some(models_array) = json.as_array() {
            // Direct array of strings: ["model1", "model2"]
            models_array.iter()
                .filter_map(|m| m.as_str().map(|s| s.to_string()))
                .collect()
        } else {
            // Fallback: single model or unknown format
            vec!["custom-model".to_string()]
        };
        
        Ok(models)
    }
    
    async fn send_message(&self, model: &str, prompt: &str) -> Result<reqwest::Response, AdapterError> {
        let chat_url = self.get_endpoint("chat");
        let mut template = self.get_request_template("chat");
        
        // Replace placeholders in the template
        let template_str = template.to_string()
            .replace("{prompt}", prompt)
            .replace("{model}", model);
        
        let body: serde_json::Value = serde_json::from_str(&template_str)?;
        
        let response = self.client.post(&chat_url).json(&body).send().await?;
        
        if !response.status().is_success() {
            return Err(AdapterError::Custom(format!("Server returned status: {}", response.status())));
        }
        
        Ok(response)
    }
    
    async fn generate_welcome_message(&self, model: &str) -> Result<String, AdapterError> {
        let prompt = "As a friendly and helpful AI assistant, introduce yourself to the user in a single, welcoming sentence.";
        let generate_url = self.get_endpoint("generate");
        let mut template = self.get_request_template("welcome");
        
        // Replace placeholders in the template
        let template_str = template.to_string()
            .replace("{prompt}", prompt)
            .replace("{model}", model);
        
        let body: serde_json::Value = serde_json::from_str(&template_str)?;
        
        let response = self.client.post(&generate_url).json(&body).send().await?;
        
        if !response.status().is_success() {
            return Err(AdapterError::Custom(format!("Server returned status: {}", response.status())));
        }
        
        let json: serde_json::Value = response.json().await?;
        
        // Try different common response formats
        let content = if let Some(response) = json.get("response").and_then(|r| r.as_str()) {
            response // Ollama-style
        } else if let Some(choices) = json.get("choices").and_then(|c| c.as_array()) {
            // OpenAI-style
            choices.first()
                .and_then(|choice| choice.get("message"))
                .and_then(|msg| msg.get("content"))
                .and_then(|content| content.as_str())
                .unwrap_or("Hello! How can I help you today?")
        } else if let Some(content) = json.get("content").and_then(|r| r.as_str()) {
            content // Direct content
        } else {
            "Hello! How can I help you today?"
        };
        
        Ok(content.trim().to_string())
    }
    
    fn get_health_endpoint(&self) -> String {
        self.get_endpoint("health")
    }
    
    fn get_server_type(&self) -> &'static str {
        "custom"
    }
    
    fn validate_profile(&self, profile: &ServerProfile) -> Result<(), AdapterError> {
        if profile.server_type.as_deref() != Some("custom") {
            return Err(AdapterError::InvalidProfile);
        }
        
        // For custom servers, we're more lenient about validation
        // Just ensure the profile is enabled and has some executables
        if !profile.enabled {
            return Err(AdapterError::Custom("Custom server profile is disabled".to_string()));
        }
        
        Ok(())
    }
}