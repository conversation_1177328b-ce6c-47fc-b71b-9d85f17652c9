import { useState, useEffect, useCallback, useRef } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';

export const useAi = () => {
  const [availableModels, setAvailableModels] = useState([]);
  const [selectedModel, setSelectedModel] = useState('');
  const [isLoadingModels, setIsLoadingModels] = useState(true);
  const [modelError, setModelError] = useState(null);
  const [isPreloading, setIsPreloading] = useState(false);
  const [preloadingError, setPreloadingError] = useState(null);
  const modelsFetched = useRef(false);
  const isMounted = useRef(true);

  useEffect(() => {
    isMounted.current = true;
    const unlistenStart = listen('preload_start', (event) => {
      if (isMounted.current) {
        setIsPreloading(true);
        setPreloadingError(null);
      }
    });
    const unlistenSuccess = listen('preload_success', (event) => {
      if (isMounted.current) {
        setIsPreloading(false);
        setPreloadingError(null);
      }
    });
    const unlistenError = listen('preload_error', (event) => {
      if (isMounted.current) {
        setIsPreloading(false);
        setPreloadingError(event.payload);
      }
    });

    return () => {
      isMounted.current = false;
      unlistenStart.then(f => f());
      unlistenSuccess.then(f => f());
      unlistenError.then(f => f());
    };
  }, []);

  const preloadModel = useCallback(async (modelName) => {
    if (!modelName) return;
    if (!isMounted.current) return;
    try {
      await invoke('preload_model_command', { modelName });
    } catch (e) {
      if (isMounted.current) {
        setPreloadingError(e.message || 'Failed to preload model');
      }
    }
  }, []);

  const fetchAvailableModels = useCallback(async () => {
    if (modelsFetched.current) return;

    if (isMounted.current) {
        setIsLoadingModels(true);
        setModelError(null);
    }

    try {
      const models = await invoke('get_models_command');
      if (!isMounted.current) return;

      if (!models || !Array.isArray(models) || models.length === 0) {
        throw new Error('No models available. Please install at least one model.');
      }

      setAvailableModels(models);
      modelsFetched.current = true;

      if (!selectedModel || !models.includes(selectedModel)) {
        const firstModel = models[0];
        setSelectedModel(firstModel);
        if (firstModel) {
          preloadModel(firstModel);
        }
      }
    } catch (error) {
      if (isMounted.current) {
        console.error('❌ Failed to fetch models:', error);
        setModelError(error.message || 'Failed to load models. Please check your connection and try again.');
      }
    } finally {
      if (isMounted.current) {
        setIsLoadingModels(false);
      }
    }
  }, [selectedModel, preloadModel]);

  useEffect(() => {
    fetchAvailableModels();
  }, [fetchAvailableModels]);

  return {
    availableModels,
    selectedModel,
    setSelectedModel,
    isLoadingModels,
    isPreloading,
    modelError,
    preloadingError,
    fetchAvailableModels,
    preloadModel,
  };
};