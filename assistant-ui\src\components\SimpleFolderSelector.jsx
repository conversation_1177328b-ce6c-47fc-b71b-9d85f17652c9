import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFolder, faFile, faFolderOpen, faExclamationTriangle, faCheckCircle } from '@fortawesome/free-solid-svg-icons';

import { Button } from "./ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card";

const SimpleFolderSelector = ({
  label,
  description,
  fetchCommand,
  saveCommand,
  onFolderSelected
}) => {
  const [currentPath, setCurrentPath] = useState('');
  const [directoryInfo, setDirectoryInfo] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState({ text: '', type: '' });

  // Load current path on component mount
  useEffect(() => {
    loadCurrentPath();
  }, [fetchCommand]);

  const loadCurrentPath = async () => {
    if (!fetchCommand) return;
    
    try {
      const pathValue = await invoke(fetchCommand);
      setCurrentPath(pathValue || '');
      if (pathValue) {
        await loadDirectoryInfo(pathValue);
      }
    } catch (error) {
      console.error(`Error loading current path:`, error);
      setCurrentPath('');
    }
  };

  const loadDirectoryInfo = async (path) => {
    if (!path) return;
    
    try {
      const info = await invoke('get_directory_info', { path });
      setDirectoryInfo(info);
    } catch (error) {
      console.error('Error loading directory info:', error);
      setDirectoryInfo({
        path,
        exists: false,
        files: [],
        directories: [],
        error: 'Failed to load directory info'
      });
    }
  };

  const handleSelectFolder = async () => {
    setIsLoading(true);
    setMessage({ text: '', type: '' });

    try {
      // Open folder selection dialog
      const result = await invoke('dialog_open', { dialog_type: 'openDirectory' });
      
      if (result && !result.canceled && result.file_paths && result.file_paths.length > 0) {
        const selectedPath = result.file_paths[0];
        
        // Save to preferences automatically
        if (saveCommand) {
          await invoke(saveCommand, { path: selectedPath });
        }
        
        // Update UI
        setCurrentPath(selectedPath);
        await loadDirectoryInfo(selectedPath);
        
        // Notify parent component
        if (onFolderSelected) {
          onFolderSelected(selectedPath);
        }
        
        setMessage({ text: 'Folder selected and saved successfully!', type: 'success' });
      }
    } catch (error) {
      console.error('Error selecting folder:', error);
      setMessage({ text: 'Failed to select folder. Please try again.', type: 'error' });
    } finally {
      setIsLoading(false);
    }
  };

  const renderDirectoryContents = () => {
    if (!directoryInfo) return null;

    if (!directoryInfo.exists) {
      return (
        <div className="flex items-center text-red-600 text-sm p-3">
          <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
          Folder does not exist
        </div>
      );
    }

    if (directoryInfo.error) {
      return (
        <div className="flex items-center text-red-600 text-sm p-3">
          <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
          {directoryInfo.error}
        </div>
      );
    }

    const totalItems = directoryInfo.directories.length + directoryInfo.files.length;
    
    if (totalItems === 0) {
      return (
        <div className="text-muted-foreground text-sm p-3">
          Folder is empty
        </div>
      );
    }

    return (
      <div className="p-3">
        <div className="text-sm font-medium mb-3 text-muted-foreground">
          {directoryInfo.directories.length} folders, {directoryInfo.files.length} files
        </div>
        
        <div className="max-h-40 overflow-y-auto space-y-1">
          {directoryInfo.directories.slice(0, 8).map((dir, index) => (
            <div key={`dir-${index}`} className="flex items-center text-blue-600 text-sm">
              <FontAwesomeIcon icon={faFolder} className="mr-2 h-3 w-3" />
              {dir}
            </div>
          ))}
          {directoryInfo.files.slice(0, 8).map((file, index) => (
            <div key={`file-${index}`} className="flex items-center text-muted-foreground text-sm">
              <FontAwesomeIcon icon={faFile} className="mr-2 h-3 w-3" />
              {file}
            </div>
          ))}
          {totalItems > 16 && (
            <div className="text-xs text-muted-foreground italic pt-2">
              ... and {totalItems - 16} more items
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div>
        <h3 className="text-lg font-semibold">{label}</h3>
        {description && (
          <p className="text-sm text-muted-foreground mt-1">{description}</p>
        )}
      </div>

      {/* Current Selection */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-base flex items-center">
              <FontAwesomeIcon 
                icon={currentPath ? faCheckCircle : faFolderOpen} 
                className={`mr-2 h-4 w-4 ${currentPath ? 'text-green-600' : 'text-muted-foreground'}`} 
              />
              {currentPath ? 'Selected Folder' : 'No Folder Selected'}
            </CardTitle>
            <Button 
              onClick={handleSelectFolder} 
              disabled={isLoading}
              size="sm"
            >
              <FontAwesomeIcon icon={faFolderOpen} className="mr-2 h-4 w-4" />
              {isLoading ? 'Selecting...' : 'Select Folder'}
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          {currentPath ? (
            <div>
              <div className="text-sm font-mono bg-muted p-2 rounded mb-3 break-all">
                {currentPath}
              </div>
              {renderDirectoryContents()}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <FontAwesomeIcon icon={faFolderOpen} className="h-12 w-12 mb-3 opacity-50" />
              <p>Click "Select Folder" to choose a directory</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Status Message */}
      {message.text && (
        <div className={`text-sm p-3 rounded ${
          message.type === 'success' 
            ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300' 
            : 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
        }`}>
          <FontAwesomeIcon 
            icon={message.type === 'success' ? faCheckCircle : faExclamationTriangle} 
            className="mr-2" 
          />
          {message.text}
        </div>
      )}
    </div>
  );
};

export default SimpleFolderSelector;
