import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faFolder, 
  faFile, 
  faRobot, 
  faCog, 
  faRefresh,
  faDownload,
  faHardDrive
} from '@fortawesome/free-solid-svg-icons';

const ModelFileViewer = ({ modelPath, onRefresh }) => {
  const [modelFiles, setModelFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const fetchModelFiles = async () => {
    // Force absolute path - ignore whatever is passed in
    const absolutePath = "E:\\TheCollective\\Storage\\System\\Models";

    setLoading(true);
    setError('');

    try {
      console.log('Fetching model files from FORCED absolute path:', absolutePath);
      const files = await invoke('list_model_files', { dirPath: absolutePath });
      setModelFiles(files);
    } catch (err) {
      console.error('Error fetching model files:', err);
      setError(`Failed to load model files: ${err.message || err}`);
      setModelFiles([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchModelFiles();
  }, [modelPath]);

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (file) => {
    if (file.is_dir) return faFolder;
    if (file.is_model_file) return faRobot;
    if (file.file_type === 'json' || file.name.includes('config')) return faCog;
    return faFile;
  };

  const getFileTypeColor = (file) => {
    if (file.is_dir) return 'text-blue-600';
    if (file.is_model_file) return 'text-green-600';
    if (file.file_type === 'json' || file.name.includes('config')) return 'text-orange-600';
    return 'text-gray-600';
  };

  const handleRefresh = () => {
    fetchModelFiles();
    if (onRefresh) onRefresh();
  };

  if (!modelPath) {
    return (
      <div className="p-4 text-center text-gray-500">
        <FontAwesomeIcon icon={faHardDrive} className="h-8 w-8 mb-2" />
        <p>Select a model storage path to view files</p>
      </div>
    );
  }

  return (
    <div className="model-file-viewer">
      <div className="mb-4">
        <h4 className="text-md font-semibold">Model Files & Folders</h4>
      </div>

      {loading && (
        <div className="text-center py-4">
          <FontAwesomeIcon icon={faRefresh} className="animate-spin h-6 w-6 text-blue-500" />
          <p className="mt-2 text-sm text-gray-600">Loading model files...</p>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 rounded p-3 mb-4">
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}

      {!loading && !error && modelFiles.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <FontAwesomeIcon icon={faFolder} className="h-12 w-12 mb-3" />
          <p>No files found in the selected directory</p>
          <p className="text-sm mt-1">Make sure the path contains model files or folders</p>
        </div>
      )}

      {!loading && !error && modelFiles.length > 0 && (
        <div className="space-y-1 max-h-96 overflow-y-auto">
          {modelFiles.map((file, index) => (
            <div
              key={index}
              className={`flex items-center justify-between p-2 rounded hover:bg-gray-50 ${
                file.is_model_file ? 'bg-green-50 border-l-4 border-green-500' : ''
              }`}
            >
              <div className="flex items-center flex-1 min-w-0">
                <FontAwesomeIcon
                  icon={getFileIcon(file)}
                  className={`h-4 w-4 mr-3 flex-shrink-0 ${getFileTypeColor(file)}`}
                />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {file.name}
                    {file.is_dir && '/'}
                  </p>
                  <p className="text-xs text-gray-500 truncate">
                    {file.is_dir ? 'Folder' : `${file.file_type.toUpperCase()} • ${formatFileSize(file.size)}`}
                  </p>
                </div>
              </div>
              
              {file.is_model_file && (
                <div className="flex-shrink-0 ml-2">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <FontAwesomeIcon icon={faRobot} className="h-3 w-3 mr-1" />
                    Model
                  </span>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {!loading && !error && modelFiles.length > 0 && (
        <div className="mt-4 pt-3 border-t text-xs text-gray-500">
          <div className="flex justify-between">
            <span>Total: {modelFiles.length} items</span>
            <span>
              Model files: {modelFiles.filter(f => f.is_model_file).length} • 
              Folders: {modelFiles.filter(f => f.is_dir).length}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ModelFileViewer;
