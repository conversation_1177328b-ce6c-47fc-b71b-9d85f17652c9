import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import ErrorPopup from './ErrorPopup';
import { useIsMounted } from '../hooks/useIsMounted.js';
import { invoke } from '@tauri-apps/api/core';
import TabSystem from './TabSystem';
import StatusBar from '../components/StatusBar';
import FloatingChat from './FloatingChat.jsx';

// Simple ErrorBoundary component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('MainLayout Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <ErrorPopup
          title="Application Error"
          message="An error occurred in the application. Please try reloading the page."
          error={this.state.error}
          onReload={() => window.location.reload()}
        />
      );
    }

    return this.props.children;
  }
}

export const MainLayout = ({ children, tabs, addTab, closeTab }) => {
  const location = useLocation();
  const isChatPage = location.pathname === '/';
  const [selectedModel, setSelectedModel] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const isMounted = useIsMounted();

  // Load available models for floating chat
  useEffect(() => {
    const loadModels = async () => {
      try {
        const models = await invoke('get_models_command');
        if (models && models.length > 0 && isMounted()) {
          setSelectedModel(models[0].name);
        }
      } catch (error) {
        console.error('Failed to load models for floating chat:', error);
      }
    };

    if (!isChatPage) {
      loadModels();
    }
  }, [isChatPage, isMounted]);

  // Handle floating chat submission
  const handleFloatingChatSubmit = async (message) => {
    if (!selectedModel) {
      console.error('No model selected for floating chat');
      return;
    }

    if (isMounted()) {
      setIsLoading(true);
    }
    try {
      const response = await invoke('send_chat_message_command', {
        modelName: selectedModel,
        prompt: message
      });

      // For now, just log the response. In the future, you might want to show it in a notification
      console.log('Floating chat response:', response);

      // You could add a toast notification here to show the response
      if (isMounted()) {
        alert(`AI Response: ${response}`);
      }

    } catch (error) {
      console.error('Floating chat error:', error);
      if (isMounted()) {
        alert(`Error: ${error.message || error}`);
      }
    } finally {
      if (isMounted()) {
        setIsLoading(false);
      }
    }
  };

  return (
    <ErrorBoundary>
      <div className="flex flex-col h-screen bg-background text-foreground">
        <div className="flex-1 flex flex-col overflow-hidden">
          <ErrorBoundary>
            <TabSystem tabs={tabs} addTab={addTab} closeTab={closeTab} />
          </ErrorBoundary>
          <main className="flex-1 overflow-auto">
            {children}
          </main>
        </div>
        {!isChatPage && (
          <FloatingChat
            onSubmit={handleFloatingChatSubmit}
            isLoading={isLoading}
          />
        )}
        <StatusBar />
      </div>
    </ErrorBoundary>
  );
};