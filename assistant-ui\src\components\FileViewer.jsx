import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';

const FileViewer = ({ filePath }) => {
  const [content, setContent] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchFileContent = async () => {
      if (!filePath) {
        setContent('');
        setError('No file path provided.');
        return;
      }

      try {
        // Use the correct Tauri command name
        const fileContent = await invoke('get_file_content', {
          file_path: filePath,
        });
        setContent(fileContent);
        setError('');
      } catch (err) {
        console.error('Error reading file content:', err);
        setError(`Failed to read file: ${err.message || err}`);
        setContent('');
      }
    };

    fetchFileContent();
  }, [filePath]);

  return (
    <div className="file-viewer">
      {error && <p className="error-message">{error}</p>}
      {!error && content && (
        <pre className="file-content">
          <code>{content}</code>
        </pre>
      )}
      {!error && !content && !filePath && <p>Select a file to view its content.</p>}
      {!error && !content && filePath && <p>Loading file content...</p>}
    </div>
  );
};

export default FileViewer;