// src-tauri/src/command_processor.rs

use tauri::{command, State};
use std::collections::HashMap;
use std::sync::Mutex;

pub struct CommandProcessorState {
    pub context: Mutex<HashMap<String, String>>,
}

impl Default for CommandProcessorState {
    fn default() -> Self {
        Self {
            context: Mutex::new(HashMap::new()),
        }
    }
}

#[command]
pub async fn process_advanced_command(
    command_string: String,
    state: State<'_, CommandProcessorState>,
) -> Result<String, String> {
    let parts: Vec<&str> = command_string.split_inclusive("&&").collect();
    let mut overall_chain_success = true;
    let mut results: Vec<String> = Vec::new();

    for part in parts {
        let sub_commands: Vec<&str> = part.split_inclusive("||").collect();
        let mut current_part_success = false; // Tracks success for the current '&&' separated part

        for sub_cmd_with_op in sub_commands {
            let (cmd, op) = if sub_cmd_with_op.ends_with("&&") {
                (sub_cmd_with_op.trim_end_matches("&&").trim(), "&&")
            } else if sub_cmd_with_op.ends_with("||") {
                (sub_cmd_with_op.trim_end_matches("||").trim(), "||")
            } else {
                (sub_cmd_with_op.trim(), "")
            };

            if cmd.is_empty() {
                continue;
            }

            // Simulate command execution and result
            // Example of context usage: set a variable if command is 'set_var:key=value'
            if cmd.starts_with("set_var:") {
                let parts: Vec<&str> = cmd.splitn(2, ':').collect();
                if parts.len() == 2 {
                    let key_value: Vec<&str> = parts[1].splitn(2, '=').collect();
                    if key_value.len() == 2 {
                        let mut context = state.context.lock().unwrap();
                        context.insert(key_value[0].to_string(), key_value[1].to_string());
                        println!("Context updated: {} = {}", key_value[0], key_value[1]);
                        results.push(format!("Context set: {} = {}", key_value[0], key_value[1]));
                        continue;
                    }
                }
            }

            // Example of context usage: get a variable if command is 'get_var:key'
            if cmd.starts_with("get_var:") {
                let key = cmd.trim_start_matches("get_var:");
                let context = state.context.lock().unwrap();
                if let Some(value) = context.get(key) {
                    println!("Context value for {}: {}", key, value);
                    results.push(format!("Context get: {} = {}", key, value));
                } else {
                    println!("Context key '{}' not found.", key);
                    results.push(format!("Context get: Key '{}' not found.", key));
                }
                continue;
            }

            let command_result = if cmd.contains("fail") {
                Err(format!("Command '{}' failed.", cmd))
            } else {
                Ok(format!("Command '{}' executed successfully.", cmd))
            };

            let current_cmd_succeeded = command_result.is_ok();
            results.push(command_result.unwrap_or_else(|e| e));

            match op {
                "&&" => {
                    current_part_success = current_cmd_succeeded;
                    if !current_part_success {
                        break; // Break from inner '||' loop if '&&' fails
                    }
                }
                "||" => {
                    if current_cmd_succeeded {
                        current_part_success = true;
                        break; // Break from inner '||' loop if '||' succeeds
                    }
                }
                _ => {
                    current_part_success = current_cmd_succeeded;
                }
            }
        }
        overall_chain_success = current_part_success;
        if !overall_chain_success && part.contains("&&") {
            break;
        }
    }

    if overall_chain_success {
        Ok(results.join("\n"))
    } else {
        Err(results.join("\n"))
    }}

#[tauri::command]
pub async fn run_command(command: String, _state: State<'_, CommandProcessorState>) -> Result<String, String> {
    Ok(format!("Run command: {}", command))
}

#[tauri::command]
pub async fn check_command_status(command_id: String, _state: State<'_, CommandProcessorState>) -> Result<String, String> {
    Ok(format!("Status for command_id: {} is running", command_id))
}

#[tauri::command]
pub async fn stop_command(command_id: String, _state: State<'_, CommandProcessorState>) -> Result<String, String> {
    Ok(format!("Stopped command_id: {}", command_id))
}