import React, { useState, useEffect, useRef, useMemo } from 'react';
import { BrowserRouter as Router, Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import './App.css';
import Dashboard from './pages/Dashboard.jsx';
import Settings from './pages/Settings.jsx';
import Tools from './pages/Tools.jsx';
import { MainLayout } from './components/MainLayout.jsx';
import { SettingsProvider, useSettings } from './contexts/SettingsContext.jsx';
import PluginLoader from './components/PluginLoader.jsx';
import ErrorBoundary from './components/ErrorBoundary.jsx';
import ErrorPopup from './components/ErrorPopup.jsx';
import { useIsMounted } from './hooks/useIsMounted.js';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faHome, faComments, faTools, faCog, faFolder } from '@fortawesome/free-solid-svg-icons';

// Define initialization steps with default states
const INIT_STEPS = [
  { id: 'start', label: 'Starting Application', status: 'pending' },
  { id: 'servers', label: 'Initializing Server Connections', status: 'pending' },
  { id: 'models', label: 'Loading AI Models', status: 'pending' },
  { id: 'plugins', label: 'Loading Plugins', status: 'pending' },
  { id: 'chat', label: 'Preparing Chat System', status: 'pending' },
  { id: 'complete', label: 'Ready', status: 'pending' },
];

const LoadingScreen = ({ message = 'Initializing...', currentStep = 0, error = null }) => {
  if (error) {
    return (
      <ErrorPopup
        title="Initialization Error"
        message="An error occurred while initializing the application."
        error={error}
        onReload={() => window.location.reload()}
      />
    );
  }

  return (
    <div className="flex items-center justify-center w-full h-full bg-background text-foreground p-6">
      <div className="w-full max-w-2xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-primary mb-2">The Collective</h1>
          <p className="text-muted-foreground">Initializing your workspace</p>
        </div>
        
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mb-4"></div>
          <p className="text-muted-foreground">Please wait...</p>
        </div>
      </div>
    </div>
  );
};

const AppContent = () => {
  const { isLoading, error } = useSettings();
  const location = useLocation();
  const [tauriReady, setTauriReady] = useState(false);
  const [initializationComplete, setInitializationComplete] = useState(false);
  const [initializationMessage, setInitializationMessage] = useState('Initializing application...');
  const [tabs, setTabs] = useState([
    { id: 'dashboard', name: 'Dashboard', icon: faHome, path: '/', component: Dashboard },
    { id: 'chat', name: 'Chat', icon: faComments, path: '/chat', component: (props) => <PluginLoader pluginName="Chat" {...props} /> },
    { id: 'tools', name: 'Tools', icon: faTools, path: '/tools', component: Tools },
    { id: 'settings', name: 'Settings', icon: faCog, path: '/settings', component: Settings },
  ]);
  const navigate = useNavigate();
  const isMounted = useIsMounted();
  const tabsRef = useRef(tabs);

  // Keep tabsRef updated
  useEffect(() => {
    tabsRef.current = tabs;
  }, [tabs]);

  useEffect(() => {
    let unlistenStart = null;
    let unlistenProgress = null;
    let unlistenComplete = null;
    let initializationStartTime = Date.now();

    // Cleanup function for event listeners
    const cleanupListeners = () => {
      [unlistenStart, unlistenProgress, unlistenComplete].forEach(unlisten => {
        if (unlisten && typeof unlisten === 'function') {
          try {
            unlisten();
          } catch (error) {
            console.warn('Error cleaning up event listener:', error);
          }
        }
      });
      unlistenStart = null;
      unlistenProgress = null;
      unlistenComplete = null;
    };

    const initializeTauri = async () => {
      console.log('=== FRONTEND APP INITIALIZATION ===');
      
      if (!window.__TAURI__) {
        console.log('Running in browser mode, skipping Tauri initialization');
        if (isMounted()) {
          setTauriReady(true);
          setInitializationComplete(true);
        }
        return;
      }

      try {
        const { appWindow } = await import('@tauri-apps/api/window');
        await appWindow.show();
      
        // Set global error handlers
        window.onerror = (message, source, lineno, colno, error) => {
          console.error('Uncaught error:', { message, source, lineno, colno, error });
          return true; // Prevent default error handler
        };

        window.onunhandledrejection = (event) => {
          console.error('Unhandled promise rejection:', event.reason);
          if (isMounted()) {
            setInitializationMessage(`Error: ${event.reason?.message || 'Unknown error during initialization'}`);
          }
        };

      // Listen for initialization events
      const { listen } = await import('@tauri-apps/api/event');

      unlistenStart = await listen('initialization_start', (event) => {
        console.log('🚀 Initialization started:', event.payload);
        if (isMounted()) {
          setInitializationMessage(event.payload);
        }
        initializationStartTime = Date.now();
      });

      unlistenProgress = await listen('initialization_progress', (event) => {
        console.log('⏳ Initialization progress:', event.payload);
        if (isMounted()) {
          setInitializationMessage(event.payload);
        }
      });

      unlistenComplete = await listen('initialization_complete', (event) => {
        console.log('✅ Initialization complete:', event.payload);
        if (isMounted()) {
          setInitializationMessage(event.payload);
        }
        if (isMounted()) {
          setInitializationComplete(true);
        }
      });

      console.log('✅ Frontend ready - backend will handle initialization');
      if (isMounted()) {
        setTauriReady(true);
      }
      
    } catch (error) {
      console.error('Error initializing Tauri:', error);
      if (isMounted()) {
        setInitializationMessage(`Initialization error: ${error.message}`);
        setTauriReady(true);
        setInitializationComplete(true); // Don't block the UI on error
      }
    }
  };

  // Start the initialization process
  initializeTauri();

  // Cleanup function
  return () => {
    cleanupListeners();
  };
}, [isMounted]);

  // Show loading screen only for critical loading states
  if (isLoading || !tauriReady) {
    return <LoadingScreen />;
  }

  // Show loading screen for initialization
  if (!initializationComplete) {
    return <LoadingScreen />;
  }

  if (error) {
    return (
      <ErrorPopup
        title="Settings Error"
        message="An error occurred while loading application settings."
        error={error}
        onReload={() => window.location.reload()}
      />
    );
  }

  const addTab = (app) => {
    // Use PluginLoader for all modular apps
    let component;
    
    // Map app names to plugin components
    if (app.id === 'chat-assistant' || app.name === 'Chat Assistant' || app.name === 'Chat') {
      component = (props) => <PluginLoader pluginName="Chat" {...props} />;
    } else if (app.id === 'file-explorer' || app.name === 'File Explorer') {
      component = (props) => <PluginLoader pluginName="FileExplorer" {...props} />;
    } else if (app.id === 'browser' || app.name === 'Browser') {
      component = (props) => <PluginLoader pluginName="Browser" {...props} />;
    } else {
      // Fallback for other apps - could be expanded to support more plugins
      component = (props) => <PluginLoader pluginName={app.name} {...props} />;
    }
    
    const newTab = {
      id: `${app.id}-${Date.now()}`,
      name: app.name,
      icon: app.icon,
      path: `/${app.id}-${Date.now()}`,
      component: component,
    };
    
    // Ensure we only update state if component is still mounted
    if (isMounted()) {
      setTabs(prevTabs => [...prevTabs, newTab]);
      // Use queueMicrotask to prevent race conditions with state updates
      queueMicrotask(() => {
        if (isMounted()) {
          navigate(newTab.path);
        }
      });
    }
  };

  const closeTab = (tabId) => {
    console.log('closeTab called with:', { tabId, currentPath: location.pathname });
    
    // Defensive check for valid tabId
    if (!tabId) {
      console.warn('Invalid tabId for closing:', tabId);
      return;
    }
    
    const tabIndex = tabsRef.current.findIndex(tab => tab && tab.id === tabId);
    const targetTab = tabsRef.current[tabIndex];
    
    if (!targetTab) {
      console.warn('Tab not found for closing:', tabId);
      return;
    }
    
    // Prevent race conditions by using functional state update
    setTabs(currentTabs => {
      // Defensive filtering to ensure we only have valid tabs
      const validTabs = currentTabs.filter(tab => tab && tab.id);
      const updatedTabs = validTabs.filter(tab => tab.id !== tabId);
      
      // If we're closing the currently active tab, navigate to a different one
      if (targetTab.path === location.pathname && updatedTabs.length > 0) {
        // Find the tab to navigate to (prefer previous tab, fallback to first tab)
        const newActiveTab = updatedTabs[Math.max(0, tabIndex - 1)] || updatedTabs[0];
        if (newActiveTab && newActiveTab.path) {
          // Use setTimeout with 0 delay to prevent race conditions with state updates
          // This gives the browser a chance to finish any pending DOM operations
          setTimeout(() => {
            if (isMounted()) {
              navigate(newActiveTab.path);
            }
          }, 0);
        }
      }
      
      return updatedTabs;
    });
  };

  // Memoize the routes to prevent unnecessary re-renders
  const routes = useMemo(() => {
    return tabs
      .filter(tab => tab && tab.id && tab.path && tab.component)
      .map(tab => {
        // Ensure we have a valid component
        if (!tab.component) {
          console.warn('Tab missing component:', tab.id);
          return null;
        }
        
        // Use a simple, stable key based only on the tab id
        // This prevents reconciliation issues when paths change
        const key = `route-${tab.id}`;
        
        return (
          <Route 
            key={key}
            path={tab.path} 
            element={
              <ErrorBoundary>
                <tab.component addTab={addTab} />
              </ErrorBoundary>
            } 
          />
        );
      })
      .filter(Boolean);
  }, [tabs, addTab]);

  return (
    <MainLayout tabs={tabs} addTab={addTab} closeTab={closeTab}>
      <Routes>
        {routes}
      </Routes>
    </MainLayout>
  );
};

const App = () => (
  <ErrorBoundary>
    <Router>
      <SettingsProvider>
        <AppContent />
      </SettingsProvider>
    </Router>
  </ErrorBoundary>
);

export default App;