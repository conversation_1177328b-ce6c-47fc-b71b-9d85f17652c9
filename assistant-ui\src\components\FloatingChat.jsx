import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faChevronUp, 
  faChevronDown, 
  faMicrophone, 
  faPaperclip, 
  faPaperPlane 
} from '@fortawesome/free-solid-svg-icons';
import { Button } from './ui/button.jsx';
import { Input } from './ui/input.jsx';

const FloatingChat = ({ onSubmit, isLoading = false }) => {
  const [isInputVisible, setIsInputVisible] = useState(false);
  const [input, setInput] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    if (input.trim() && onSubmit) {
      onSubmit(input.trim());
      setInput('');
    }
  };

  const handleFiles = (files) => {
    if (files && files.length > 0) {
      console.log('Files selected:', files);
      // Handle file upload logic here
    }
  };

  return (
    <div className="fixed bottom-8 right-6 z-50 pointer-events-none"> {/* Increased z-index to avoid conflicts */}
      <div className="flex flex-col items-end pointer-events-auto">
        {/* Floating Input Container - appears above button when open */}
        <div
          className={`transition-all duration-500 ease-in-out transform ${
            isInputVisible ? 'translate-y-0 opacity-100 mb-3' : 'translate-y-full opacity-0 mb-0 pointer-events-none'
          }`}
        >
          <div className="bg-card border border-border rounded-xl shadow-lg p-2 max-w-xs backdrop-blur-sm">
            <form className="flex items-center space-x-1.5" onSubmit={handleSubmit}>
              <div className="flex-1 relative">
                <Input
                  type="text"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="Message The Collective..."
                  disabled={isLoading}
                  className="w-full pl-2 pr-8 py-1.5 bg-transparent border-0 focus:ring-0 focus:outline-none text-foreground placeholder-muted-foreground text-sm"
                />
                {input.trim() && (
                  <div className="absolute right-2 top-1/2 -translate-y-1/2 text-xs text-muted-foreground">
                    {input.length}
                  </div>
                )}
              </div>
              <Button
                variant="ghost"
                size="icon"
                type="button"
                onClick={() => alert('Microphone clicked!')}
                disabled={isLoading}
                className="h-6 w-6 hover:bg-muted text-muted-foreground hover:text-foreground transition-colors rounded-full"
              >
                <FontAwesomeIcon icon={faMicrophone} className="h-3 w-3" />
              </Button>
              <input
                type="file"
                id="file-input-floating-global"
                multiple
                onChange={(e) => handleFiles(e.target.files)}
                className="hidden"
              />
              <Button
                variant="ghost"
                size="icon"
                type="button"
                onClick={() => document.getElementById('file-input-floating-global').click()}
                disabled={isLoading}
                className="h-6 w-6 hover:bg-muted text-muted-foreground hover:text-foreground transition-colors rounded-full"
              >
                <FontAwesomeIcon icon={faPaperclip} className="h-3 w-3" />
              </Button>
              <Button
                type="submit"
                size="icon"
                disabled={isLoading || !input.trim()}
                className="h-6 w-6 bg-primary hover:bg-primary/90 text-primary-foreground shadow-md hover:shadow-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed rounded-full"
              >
                <FontAwesomeIcon icon={faPaperPlane} className="h-2.5 w-2.5" />
              </Button>
            </form>
          </div>
        </div>

        {/* Toggle Button - always visible but subtle, fixed cursor */}
        <button
          onClick={() => setIsInputVisible(!isInputVisible)}
          className="opacity-40 hover:opacity-100 transition-all duration-300 p-2 bg-card border border-border rounded-full shadow-md hover:shadow-lg cursor-pointer"
          style={{ cursor: 'pointer' }} // Force cursor to be pointer
        >
          <FontAwesomeIcon
            icon={isInputVisible ? faChevronDown : faChevronUp}
            className="h-3 w-3 text-muted-foreground hover:text-foreground transition-colors"
          />
        </button>
      </div>
    </div>
  );
};

export default FloatingChat;
