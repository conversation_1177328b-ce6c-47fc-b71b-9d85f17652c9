import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFolder, faFile, faRefresh, faExclamationTriangle } from '@fortawesome/free-solid-svg-icons';

import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Card, CardContent } from "./ui/card";

const EnhancedPathSelector = ({
  id,
  label,
  description,
  fetchCommand,
  saveCommand,
  dialogType = 'openDirectory',
  placeholderText = 'Enter path',
  saveButtonText = 'Save Path',
  onPathSelected
}) => {
  const [currentPath, setCurrentPath] = useState('');
  const [newPath, setNewPath] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState({ text: '', type: '' });
  const [directoryInfo, setDirectoryInfo] = useState(null);
  const [loadingDirectory, setLoadingDirectory] = useState(false);

  const fetchData = async () => {
    if (!fetchCommand) return;
    setIsLoading(true);
    try {
      const pathValue = await invoke(fetchCommand);
      setCurrentPath(pathValue || '');
      setNewPath(pathValue || '');
      setMessage({ text: '', type: '' });
      
      // Fetch directory info for the current path
      if (pathValue) {
        await fetchDirectoryInfo(pathValue);
      }
    } catch (error) {
      console.error(`Error fetching path with command ${fetchCommand}:`, error);
      setMessage({ text: `Error fetching current path. Command may not be implemented yet.`, type: 'error' });
      setCurrentPath('');
      setNewPath('');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchDirectoryInfo = async (path) => {
    if (!path) return;
    setLoadingDirectory(true);
    try {
      const info = await invoke('get_directory_info', { path });
      setDirectoryInfo(info);
    } catch (error) {
      console.error('Error fetching directory info:', error);
      setDirectoryInfo({
        path,
        exists: false,
        files: [],
        directories: [],
        error: 'Failed to fetch directory info'
      });
    } finally {
      setLoadingDirectory(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [fetchCommand]);



  const handleSave = async () => {
    if (!saveCommand || !newPath.trim()) {
      setMessage({ text: 'Please enter a valid path.', type: 'error' });
      return;
    }

    setIsLoading(true);
    try {
      const result = await invoke(saveCommand, { path: newPath.trim() });
      setCurrentPath(newPath.trim());
      setMessage({ text: result || 'Path saved successfully!', type: 'success' });
      
      // Refresh directory info after saving
      await fetchDirectoryInfo(newPath.trim());
    } catch (error) {
      console.error(`Error saving path with command ${saveCommand}:`, error);
      setMessage({ text: `Error saving path: ${error}`, type: 'error' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    if (currentPath) {
      await fetchDirectoryInfo(currentPath);
    }
  };

  const renderDirectoryContents = () => {
    if (loadingDirectory) {
      return (
        <div className="text-sm text-muted-foreground p-2">
          Loading directory contents...
        </div>
      );
    }

    if (!directoryInfo) {
      return null;
    }

    if (!directoryInfo.exists) {
      return (
        <div className="text-sm text-red-600 p-2 flex items-center">
          <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
          Directory does not exist
        </div>
      );
    }

    if (directoryInfo.error) {
      return (
        <div className="text-sm text-red-600 p-2 flex items-center">
          <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
          {directoryInfo.error}
        </div>
      );
    }

    const totalItems = directoryInfo.directories.length + directoryInfo.files.length;
    
    if (totalItems === 0) {
      return (
        <div className="text-sm text-muted-foreground p-2">
          Directory is empty
        </div>
      );
    }

    return (
      <div className="text-sm">
        <div className="flex items-center justify-between mb-2">
          <span className="font-medium">
            {directoryInfo.directories.length} folders, {directoryInfo.files.length} files
          </span>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            disabled={loadingDirectory}
          >
            <FontAwesomeIcon icon={faRefresh} className="h-3 w-3" />
          </Button>
        </div>
        
        <div className="max-h-32 overflow-y-auto space-y-1">
          {directoryInfo.directories.slice(0, 10).map((dir, index) => (
            <div key={`dir-${index}`} className="flex items-center text-blue-600">
              <FontAwesomeIcon icon={faFolder} className="mr-2 h-3 w-3" />
              {dir}
            </div>
          ))}
          {directoryInfo.files.slice(0, 10).map((file, index) => (
            <div key={`file-${index}`} className="flex items-center text-muted-foreground">
              <FontAwesomeIcon icon={faFile} className="mr-2 h-3 w-3" />
              {file}
            </div>
          ))}
          {totalItems > 20 && (
            <div className="text-xs text-muted-foreground italic">
              ... and {totalItems - 20} more items
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor={id} className="text-sm font-medium">
          {label}
        </Label>
        {description && (
          <p className="text-sm text-muted-foreground mt-1">{description}</p>
        )}
      </div>

      <div className="flex space-x-2">
        <Input
          id={id}
          type="text"
          value={newPath}
          onChange={(e) => setNewPath(e.target.value)}
          placeholder={placeholderText}
          className="flex-1"
          disabled={isLoading}
        />
        <Button
          onClick={handleSave}
          disabled={isLoading || !newPath.trim()}
        >
          {isLoading ? 'Saving...' : saveButtonText}
        </Button>
      </div>

      {message.text && (
        <div className={`text-sm ${message.type === 'error' ? 'text-red-600' : 'text-green-600'}`}>
          {message.text}
        </div>
      )}

      {currentPath && (
        <Card>
          <CardContent className="p-3">
            <div className="text-sm">
              <div className="font-medium mb-2">Current Path: {currentPath}</div>
              {renderDirectoryContents()}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default EnhancedPathSelector;
