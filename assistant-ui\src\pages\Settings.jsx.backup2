import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { Link } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCog, faPlug, faDatabase, faBoxOpen, faSearch, faUserCog,
  faBell, faPalette, faShieldAlt, faBook, faChevronLeft, faRobot, 
  faPlus, faUser, faFolder, faFile, faRefresh, faExclamationTriangle,
  faServer, faInfoCircle
} from '@fortawesome/free-solid-svg-icons';

import { Button } from "../components/ui/button.jsx";
import { Input } from "../components/ui/input.jsx";
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from "../components/ui/card.jsx";
import { Switch } from "../components/ui/switch.jsx";
import { Label } from "../components/ui/label.jsx";
import { useToast } from "../components/ui/use-toast.jsx";
import ModelsSettings from '../components/ModelsSettings.jsx';
import { useSettings } from '../contexts/SettingsContext.jsx';

const Settings = () => {
  const { toast } = useToast();
  const { userSettings, saveUserSetting, isLoading: settingsLoading, error: settingsError } = useSettings();
  const [activeMainTab, setActiveMainTab] = useState('System');
  const [activeSidebarItem, setActiveSidebarItem] = useState('General');
  const [ollamaStatus, setOllamaStatus] = useState('stopped');
  const [serverProfiles, setServerProfiles] = useState([]);
  const [modelProfiles, setModelProfiles] = useState([]);
  const [loadingProfiles, setLoadingProfiles] = useState(false);
  const [serverDirectoryContents, setServerDirectoryContents] = useState([]);
  const [modelDirectoryContents, setModelDirectoryContents] = useState([]);
  const [modelToPull, setModelToPull] = useState('');
  const [pullingModel, setPullingModel] = useState(false);
  const [pullModelMessage, setPullModelMessage] = useState('');
  const [pullModelError, setPullModelError] = useState('');
  const [downloadStatus, setDownloadStatus] = useState(null);

  // Navigation items for the sidebar
  const navigationItems = [
    { id: 'general', icon: faCog, label: 'General' },
    { id: 'server', icon: faServer, label: 'Server' },
    { id: 'models', icon: faBoxOpen, label: 'Models' },
    { id: 'storage', icon: faDatabase, label: 'Storage' },
    { id: 'plugins', icon: faPlug, label: 'Plugins' },
    { id: 'appearance', icon: faPalette, label: 'Appearance' },
    { id: 'notifications', icon: faBell, label: 'Notifications' },
    { id: 'privacy', icon: faShieldAlt, label: 'Privacy' },
    { id: 'about', icon: faInfoCircle, label: 'About' },
  ];

  // Top-level loading and error fallback
  if (settingsLoading) {
    return (
      <div className="flex items-center justify-center w-full h-full bg-background text-foreground">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-foreground mb-2">Settings</h2>
          <p className="text-muted-foreground">Loading settings...</p>
        </div>
      </div>
    );
  }
  
  if (settingsError) {
    return (
      <div className="flex items-center justify-center w-full h-full bg-background text-foreground">
        <div className="text-center">
          <FontAwesomeIcon icon={faExclamationTriangle} className="text-destructive h-12 w-12 mb-4" />
          <h2 className="text-xl font-semibold text-foreground mb-2">Error Loading Settings</h2>
          <p className="text-muted-foreground mb-4">{settingsError}</p>
          <Button variant="outline" onClick={() => window.location.reload()}>
            <FontAwesomeIcon icon={faRefresh} className="mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  // Handle server profile actions
  const handleAddServerProfile = async (profile) => {
    try {
      await invoke('add_server_profile', { profile });
      toast({
        title: 'Server Profile Added',
        description: 'The server profile has been added successfully.',
      });
      // Refresh server profiles
      await loadServerProfiles();
    } catch (error) {
      console.error('Error adding server profile:', error);
      toast({
        title: 'Error',
        description: 'Failed to add server profile. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Load server profiles
  const loadServerProfiles = async () => {
    try {
      setLoadingProfiles(true);
      const profiles = await invoke('get_server_profiles');
      setServerProfiles(profiles);
    } catch (error) {
      console.error('Error loading server profiles:', error);
      toast({
        title: 'Error',
        description: 'Failed to load server profiles.',
        variant: 'destructive',
      });
    } finally {
      setLoadingProfiles(false);
    }
  };

  // Handle model download
  const handleDownloadModel = async (modelName) => {
    try {
      setPullingModel(true);
      setPullModelMessage('');
      setPullModelError('');
      
      await invoke('download_model', { modelName });
      
      toast({
        title: 'Model Downloaded',
        description: `Successfully downloaded ${modelName}`,
      });
      
      // Refresh model list
      await loadModelProfiles();
    } catch (error) {
      console.error('Error downloading model:', error);
      setPullModelError(error.toString());
      toast({
        title: 'Download Error',
        description: `Failed to download model: ${error}`,
        variant: 'destructive',
      });
    } finally {
      setPullingModel(false);
    }
  };

  // Load model profiles
  const loadModelProfiles = async () => {
    try {
      setLoadingProfiles(true);
      const models = await invoke('get_model_profiles');
      setModelProfiles(models);
    } catch (error) {
      console.error('Error loading model profiles:', error);
      toast({
        title: 'Error',
        description: 'Failed to load model profiles.',
        variant: 'destructive',
      });
    } finally {
      setLoadingProfiles(false);
    }
  };

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      await Promise.all([
        loadServerProfiles(),
        loadModelProfiles(),
      ]);
    };
    
    loadInitialData();
  }, []);

  // Render the settings content based on active tab
  const renderSettingsContent = () => {
    switch (activeSidebarItem.toLowerCase()) {
      case 'models':
        return (
          <ModelsSettings 
            models={modelProfiles}
            onDownloadModel={handleDownloadModel}
            loading={loadingProfiles}
            downloadStatus={downloadStatus}
            pullModelError={pullModelError}
            pullModelMessage={pullModelMessage}
            pullingModel={pullingModel}
          />
        );
      case 'server':
        return (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Server Configuration</h2>
            <Card>
              <CardHeader>
                <CardTitle>Ollama Server</CardTitle>
                <CardDescription>
                  Configure your local Ollama server settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Server Status</Label>
                    <div className="flex items-center">
                      <div className={`h-2 w-2 rounded-full mr-2 ${
                        ollamaStatus === 'running' ? 'bg-green-500' : 'bg-red-500'
                      }`} />
                      <span className="capitalize">{ollamaStatus}</span>
                    </div>
                  </div>
                  <Button 
                    variant="outline" 
                    disabled={ollamaStatus === 'starting' || ollamaStatus === 'stopping'}
                    onClick={async () => {
                      try {
                        if (ollamaStatus === 'running') {
                          setOllamaStatus('stopping');
                          await invoke('stop_ollama_server');
                          setOllamaStatus('stopped');
                        } else {
                          setOllamaStatus('starting');
                          await invoke('start_ollama_server');
                          setOllamaStatus('running');
                        }
                      } catch (error) {
                        console.error('Error toggling Ollama server:', error);
                        toast({
                          title: 'Error',
                          description: `Failed to toggle Ollama server: ${error}`,
                          variant: 'destructive',
                        });
                        setOllamaStatus(ollamaStatus === 'running' ? 'running' : 'stopped');
                      }
                    }}
                  >
                    {ollamaStatus === 'running' ? 'Stop Server' : 'Start Server'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        );
      case 'general':
      default:
        return (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">General Settings</h2>
            <Card>
              <CardHeader>
                <CardTitle>Application Settings</CardTitle>
                <CardDescription>
                  Configure general application preferences
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Start on system login</Label>
                    <p className="text-sm text-muted-foreground">
                      Launch application when your computer starts
                    </p>
                  </div>
                  <Switch 
                    checked={userSettings?.startOnLogin ?? false}
                    onCheckedChange={async (checked) => {
                      try {
                        await saveUserSetting('startOnLogin', checked);
                      } catch (error) {
                        console.error('Error updating setting:', error);
                        toast({
                          title: 'Error',
                          description: 'Failed to update setting',
                          variant: 'destructive',
                        });
                      }
                    }}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Check for updates automatically</Label>
                    <p className="text-sm text-muted-foreground">
                      Automatically check for and install updates
                    </p>
                  </div>
                  <Switch 
                    checked={userSettings?.autoUpdate ?? true}
                    onCheckedChange={async (checked) => {
                      try {
                        await saveUserSetting('autoUpdate', checked);
                      } catch (error) {
                        console.error('Error updating setting:', error);
                        toast({
                          title: 'Error',
                          description: 'Failed to update setting',
                          variant: 'destructive',
                        });
                      }
                    }}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        );
    }
  };
    return (
      <div className="flex items-center justify-center w-full h-full bg-background text-foreground">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-destructive mb-2">Settings Error</h2>
          <p className="text-muted-foreground mb-4">Failed to load settings: {settingsError}</p>
          <p className="text-sm text-muted-foreground">Using default settings...</p>
        </div>
      </div>
    );
  }

  // Remove configBaseUrl - we'll use Tauri commands instead

  // Ollama states
  const [ollamaStatus, setOllamaStatus] = useState('stopped'); // Start with stopped instead of unknown
  const [ollamaModels, setOllamaModels] = useState([]);
  const [loadingOllamaModels, setLoadingOllamaModels] = useState(false);
  const [modelToPull, setModelToPull] = useState('');
  const [pullingModel, setPullingModel] = useState(false);
  const [pullModelMessage, setPullModelMessage] = useState('');
  const [pullModelError, setPullModelError] = useState('');
  const [downloadStatus, setDownloadStatus] = useState(null);
  const [serverDirectoryContents, setServerDirectoryContents] = useState([]);
  const [modelDirectoryContents, setModelDirectoryContents] = useState([]);

  // Server and Model profile states
  const [serverProfiles, setServerProfiles] = useState([]);
  const [modelProfiles, setModelProfiles] = useState([]);
  const [activeServerProfile, setActiveServerProfile] = useState('');
  const [activeModelProfile, setActiveModelProfile] = useState('');
  const [loadingProfiles, setLoadingProfiles] = useState(false);

  useEffect(() => {
    const unlistenStart = listen('model_pull_start', (event) => {
      setDownloadStatus({
        modelName: event.payload,
        status: 'starting',
        total: 0,
        completed: 0,
      });
      setPullingModel(true);
      setPullModelMessage('');
      setPullModelError('');
    });

    const unlistenProgress = listen('model_pull_progress', (event) => {
      console.log('Progress event received:', event.payload);
      const payload = event.payload;

      // Handle Ollama's progress format
      if (payload.status && payload.total && payload.completed !== undefined) {
        setDownloadStatus({
          modelName: modelToPull || 'Unknown Model',
          status: payload.status,
          total: payload.total,
          completed: payload.completed
        });
      }
    });

    const unlistenSuccess = listen('model_pull_success', (event) => {
      setDownloadStatus(null);
      setPullingModel(false);
      setPullModelMessage(`Successfully pulled ${event.payload}`);
      // Refresh both Ollama models and model profiles
      fetchOllamaModels();
      loadModelProfiles();
      setModelToPull(''); // Clear the input
    });

    const unlistenError = listen('model_pull_error', (event) => {
      setDownloadStatus(null);
      setPullingModel(false);
      setPullModelError(`Failed to pull model: ${event.payload}`);
    });

    return () => {
      unlistenStart.then(f => f());
      unlistenProgress.then(f => f());
      unlistenSuccess.then(f => f());
      unlistenError.then(f => f());
    };
  }, []);

  // Fetch Ollama status on mount (server should already be started by App.jsx)
  useEffect(() => {
    console.log('Settings page: Fetching server status...');
    fetchOllamaStatus();
  }, []);

  const fetchOllamaStatus = async () => {
    console.log('=== FRONTEND: Fetching Ollama status ===');
    try {
      const status = await invoke('get_ollama_status');
      console.log('Raw status response:', status);
      setOllamaStatus(status.status);
      console.log('Ollama status set to:', status.status);
    } catch (error) {
      console.error('Error fetching Ollama status:', error);
      setOllamaStatus('error');
    }
  };
  

  const handleStartOllama = async () => {
    console.log('=== FRONTEND: Start button clicked ===');
    try {
      setOllamaStatus('starting');
      console.log('Starting Ollama server...');
      console.log('About to invoke start_ollama_server...');
      const result = await invoke('start_ollama_server');
      console.log('Ollama server start command completed, result:', result);
      // Wait a moment then check status
      setTimeout(() => {
        fetchOllamaStatus();
      }, 3000); // Increased wait time
    } catch (error) {
      console.error('Error starting Ollama:', error);
      setOllamaStatus('error');
      // Show the error to the user
      alert(`Failed to start Ollama server:\n\n${error}`);
    }
  };



  const handleStopOllama = async () => {
    try {
      console.log('Stopping Ollama server...');
      await invoke('stop_ollama_server');
      console.log('Ollama server stop command completed');
      setOllamaStatus('stopped');
      setTimeout(() => {
        fetchOllamaStatus();
      }, 1000);
    } catch (error) {
      console.error('Error stopping Ollama:', error);
      alert(`Failed to stop Ollama server:\n\n${error}`);
      fetchOllamaStatus();
    }
  };

  const handleCheckInstallation = async () => {
    try {
      const result = await invoke('check_ollama_installation');
      alert(`Installation Check:\n\n${result}`);
    } catch (error) {
      alert(`Installation Check Failed:\n\n${error}`);
    }
  };

  const fetchOllamaModels = async () => {
    setLoadingOllamaModels(true);
    try {
      const models = await invoke('get_ollama_models');
      setOllamaModels(models);
    } catch (error) {
      console.error('Error fetching Ollama models:', error);
      setOllamaModels([]);
    } finally {
      setLoadingOllamaModels(false);
    }
  };

  const handlePullModel = async () => {
    if (!modelToPull) return;
    console.log('Starting model pull for:', modelToPull);
    setPullingModel(true);
    setPullModelMessage('');
    setPullModelError('');
    setDownloadStatus({
      modelName: modelToPull,
      status: 'initializing',
      total: 0,
      completed: 0
    });

    try {
      await invoke('pull_ollama_model', { modelName: modelToPull });
      console.log('Model pull command sent successfully');
    } catch (error) {
      console.error('Error pulling model:', error);
      setPullModelError(`Failed to pull model: ${error}`);
      setDownloadStatus(null);
      setPullingModel(false);
    }
  };



                  <p className="text-sm text-muted-foreground">Switch between light and dark mode.</p>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={userSettings?.dark_mode || false}
                    onCheckedChange={(checked) => saveUserSetting('dark_mode', checked)}
                  />
                  <Label htmlFor="theme-toggle">
                    {userSettings?.dark_mode ? 'Dark Mode' : 'Light Mode'}
                  </Label>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      case 'System-Storage':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>Storage Settings</CardTitle>
              <CardDescription>Manage the directory to be indexed for searching.</CardDescription>
            </CardHeader>
            <CardContent>
              <SimpleFolderSelector
                label="Indexed Directory"
                description="Select the directory that the assistant should scan and index for files. Should be ./Storage/ for portable operation."
                fetchCommand="get_indexed_directory"
                saveCommand="set_indexed_directory"
                onFolderSelected={handleStoragePathSelected}
              />
            </CardContent>
          </Card>
        );
      case 'System-System Log':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>System Log Settings</CardTitle>
              <CardDescription>Configure system log storage and behavior.</CardDescription>
            </CardHeader>
            <CardContent>
              <SimpleFolderSelector
                label="System Log Path"
                description="Select the directory where system logs will be stored."
                fetchCommand="get_system_log_path"
                saveCommand="set_system_log_path"
              />
            </CardContent>
          </Card>
        );
      case 'System-Docket': // Added Docket section content under System
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>Docket Settings</CardTitle>
              <CardDescription>Configure Docket features and behavior.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Docket Panel</h4>
                  <p className="text-sm text-muted-foreground">Settings for the Docket panel will appear here.</p>
                </div>
                {/* Placeholder for actual docket content */}
              </div>
            </CardContent>
          </Card>
        );
      case 'Addons-Plugins': // Changed from Plugins-Manage Plugins
        // Deduplicate plugins
        const uniquePlugins = [...new Map(plugins.map(p => [p.name, p])).values()];
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>Plugins</CardTitle>
              <CardDescription>Enable or disable installed plugins. Configure the path to your plugins directory.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-8">
              {/* Section 1: Search Bar */}
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">Search Plugins</h3>
                <Input
                  type="search"
                  placeholder="Search plugins..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>

              {/* Section 2: Path Configuration */}
              <div className="space-y-4">
                <SimpleFolderSelector
                  label="Plugin Directory"
                  description="Select the main directory where plugins are located. The application will scan this directory for valid plugins."
                  fetchCommand="get_plugins_path"
                  saveCommand="set_plugins_path"
                  onFolderSelected={handlePluginsPathSelected}
                />
              </div>

              {/* Section 3: Plugin Display */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Installed Plugins</h3>
                {loadingPlugins && (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">Loading plugins...</p>
                  </div>
                )}

                {pluginError && (
                  <div className="text-center py-8">
                    <p className="text-destructive">{pluginError}</p>
                  </div>
                )}

                {!loadingPlugins && !pluginError && uniquePlugins.length === 0 && (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">No plugins found or installed.</p>
                  </div>
                )}

                {!loadingPlugins && !pluginError && uniquePlugins.length > 0 && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {uniquePlugins
                      .filter(plugin =>
                        plugin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        (plugin.description && plugin.description.toLowerCase().includes(searchTerm.toLowerCase()))
                      )
                      .map((plugin) => (
                        <Card key={plugin.name} className="p-4">
                          <div className="flex flex-col space-y-3">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <h4 className="font-semibold text-base">{plugin.name}</h4>
                                <span className="text-xs text-muted-foreground">v{plugin.version}</span>
                              </div>
                              <Switch
                                id={`plugin-${plugin.name}`}
                                checked={plugin.enabled}
                                onCheckedChange={() => togglePlugin(plugin.name, plugin.enabled)}
                              />
                            </div>
                            <p className="text-sm text-muted-foreground line-clamp-2">
                              {plugin.description || 'No description available.'}
                            </p>
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`plugin-${plugin.name}`} className="text-sm font-medium">
                                {plugin.enabled ? 'Enabled' : 'Disabled'}
                              </Label>
                            </div>
                          </div>
                        </Card>
                      ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        );
      case 'System-Logic': // Content for Logic (formerly Rules) under System
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>Logic Settings</CardTitle>
              <CardDescription>Configure system logic and rules.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="p-4 border rounded-lg">
                <p className="text-muted-foreground">Logic configuration options will appear here.</p>
              </div>
            </CardContent>
          </Card>
        );
      case 'Addons-MCPs':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>MCPs (Modular Cognitive Processors)</CardTitle>
              <CardDescription>Configure the path to your MCPs directory.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <SimpleFolderSelector
                label="MCPs Directory"
                description="Select the main directory where MCPs are located."
                fetchCommand="get_mcps_path"
                saveCommand="set_mcps_path"
              />
              {/* Placeholder for MCPs list or management UI */}
              <p className="mt-4 text-muted-foreground">MCP management interface will be here.</p>
            </CardContent>
          </Card>
        );
      case 'Addons-APIs':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>APIs</CardTitle>
              <CardDescription>Configure the path to your API configurations directory or file.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <SimpleFolderSelector
                label="APIs Directory"
                description="Select the directory containing API configurations or a central API configuration file."
                fetchCommand="get_apis_path"
                saveCommand="set_apis_path"
              />
              {/* Placeholder for API keys management or list */}
              <p className="mt-4 text-muted-foreground">API configuration and key management interface will be here.</p>
            </CardContent>
          </Card>
        );
      // Add more cases for other settings pages as needed
      default:
        return (
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">{activeSidebarItem}</h2>
            <Card>
              <CardContent className="p-6">
                <p className="text-muted-foreground">
                  Settings for {activeSidebarItem} will be displayed here. This is a placeholder.
                </p>
              </CardContent>
            </Card>
          </div>
        );
    }
  };

  const handleChatSubmit = (message) => {
    // Handle chat submission in settings page
    console.log('Chat message from settings:', message);
    // You can implement chat functionality here
  };

  return (
    <div className="flex flex-col h-screen bg-background text-foreground">
      <header className="flex items-center justify-between p-4 border-b border-border bg-card text-card-foreground shadow-sm">
        <div className="flex items-center space-x-4">
          <Link to="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
            <FontAwesomeIcon icon={faChevronLeft} className="h-4 w-4" />
            <span>Back to App</span>
          </Link>
          <h1 className="text-xl font-semibold">Settings</h1>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={async () => {
            try {
              await loadServerProfiles();
              await loadModelProfiles();
              toast({
                title: 'Refreshed',
                description: 'Settings and data have been refreshed.',
              });
            } catch (error) {
              console.error('Error refreshing data:', error);
              toast({
                title: 'Error',
                description: 'Failed to refresh data. Please try again.',
                variant: 'destructive',
              });
            }
          }}>
            <FontAwesomeIcon icon={faRefresh} className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>
      </header>
      
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar Navigation */}
        <div className="w-64 border-r border-border bg-muted/20 p-4 overflow-y-auto">
          <div className="space-y-1">
            {navigationItems.map((item) => (
              <button
                key={item.id}
                className={`w-full flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeSidebarItem.toLowerCase() === item.id.toLowerCase()
                    ? 'bg-primary/10 text-primary'
                    : 'text-foreground/70 hover:bg-muted/50 hover:text-foreground'
                }`}
                onClick={() => setActiveSidebarItem(item.id)}
              >
                <FontAwesomeIcon icon={item.icon} className="mr-3 h-4 w-4" />
                {item.label}
              </button>
            ))}
          </div>
        </div>
        
        {/* Main Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {renderSettingsContent()}
        </div>
      </div>
          <Link to="/" className="flex items-center space-x-2 hover:text-primary">
            <FontAwesomeIcon icon={faChevronLeft} className="h-5 w-5" />
            <span className="font-semibold text-lg">The Collective</span>
          </Link>
          <span className="text-lg text-muted-foreground">/</span>
          <span className="font-semibold text-lg">Settings</span>
        </div>
      </header>

      <Tabs value={activeMainTab} onValueChange={handleMainTabChange} className="border-b border-border">
        <TabsList className="flex justify-start px-4 pt-2 bg-card rounded-none">
          {mainTabsConfig.map(tab => (
            <TabsTrigger key={tab.name} value={tab.name} className="px-4 py-2 text-sm data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:text-primary data-[state=active]:shadow-none rounded-none">
              {tab.name}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>

      <div className="flex flex-1 overflow-hidden">
        <aside className="w-72 border-r border-border bg-card p-4 space-y-4 flex flex-col">
          <div className="relative">
            <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search settings..."
              className="pl-10 bg-background focus-visible:ring-primary focus-visible:ring-offset-0"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <nav className="flex-1 overflow-y-auto space-y-1 pr-1">
            {filteredSidebarItems.map(item => (
              <Button
                key={item.name}
                variant={activeSidebarItem === item.name ? "secondary" : "ghost"}
                className="w-full justify-start text-sm font-normal h-9"
                onClick={() => setActiveSidebarItem(item.name)}
              >
                <FontAwesomeIcon icon={item.icon} className="mr-2 h-4 w-4 text-muted-foreground" />
                {item.name}
              </Button>
            ))}
            {filteredSidebarItems.length === 0 && searchTerm && (
                <p className='text-sm text-muted-foreground text-center py-4'>No settings found for "{searchTerm}".</p>
            )}
          </nav>
        </aside>

        <main className="flex-1 overflow-y-auto bg-muted/20">
          <div className="">
            {renderContent()}
            {/* Extra scrolling space - 25% of viewport height */}
            {/* <div className="h-[25vh]"></div> */}
          </div>
        </main>
      </div>

      {/* Floating Chat System */}
      {/* <FloatingChat onSubmit={handleChatSubmit} isLoading={false} /> */}

      {/* VS Code Style Status Bar */}
      {/* <StatusBar /> */}
    </div>
  );
};

export default Settings;
