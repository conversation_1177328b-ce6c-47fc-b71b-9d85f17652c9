{"name": "Cha<PERSON>", "category": "core-tool", "version": "1.0.0", "versions": [{"version": "1.0.0", "path": "./versions/v1.0.0/", "changelog": "Initial modular implementation"}, {"version": "0.9.0", "path": "./versions/v0.9.0/", "changelog": "Legacy hardcoded version"}], "description": "AI-powered chat interface with persistent sessions", "main": "chat.jsx", "icon": "faComments", "author": "TheCollective Team", "license": "MIT", "features": {"persistent_data": true, "history_enabled": true, "state_management": true, "version_switching": true, "offline_capable": true}, "permissions": ["ai_client", "database", "file_system"], "data_structure": {"sessions": "JSON files per chat session", "history": "SQLite database with full text search", "preferences": "User customization settings"}, "integration": {"tab_system": true, "settings_panel": true, "keyboard_shortcuts": ["Ctrl+N", "Ctrl+T"]}}