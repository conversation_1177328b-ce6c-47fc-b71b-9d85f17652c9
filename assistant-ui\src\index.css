@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 220 10% 98%; /* Light background for light theme */
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    
    /* NEW: Status Colors */
    --status-online: #10B981;
    --status-offline: #6B7280;
    --status-error: #EF4444;
    --status-warning: #F59E0B;
    --status-loading: #3B82F6;
    --status-idle: #8B5CF6;
    
    /* NEW: Enhanced Shadows */
    --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.15);
    --shadow-card: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-elevated: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
  }

  .dark {
    --background: 222.2 84% 4.9%; /* Dark background for dark theme, similar to the original dark theme */
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    
    /* NEW: Enhanced Shadows for dark theme */
    --shadow-glow: 0 0 20px rgba(139, 92, 246, 0.25);
    --shadow-card: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
    --shadow-elevated: 0 8px 25px -5px rgba(0, 0, 0, 0.4);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-clamp: 2;
  }
  
  /* NEW: Enhanced shadow utilities */
  .glow-shadow {
    box-shadow: var(--shadow-glow);
  }
  .card-shadow {
    box-shadow: var(--shadow-card);
  }
  .elevated-shadow {
    box-shadow: var(--shadow-elevated);
  }
}

/* Enhanced Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

html, body {
  height: 100%;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

* {
  box-sizing: border-box;
}