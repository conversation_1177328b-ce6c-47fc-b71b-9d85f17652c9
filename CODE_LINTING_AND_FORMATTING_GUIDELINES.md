# Code Linting and Formatting Guidelines

Consistent code style is essential for readability, maintainability, and collaborative development. This document outlines the tools and guidelines for code linting and formatting in 'The Collective' application, covering both the Rust backend and the React frontend.

## Objectives

*   **Readability:** Ensure code is easy to read and understand for all developers.
*   **Consistency:** Maintain a uniform code style across the entire codebase.
*   **Maintainability:** Reduce cognitive load and make it easier to navigate and modify code.
*   **Error Prevention:** Catch potential bugs and anti-patterns early through linting.
*   **Automated Enforcement:** Integrate tools to automatically check and fix style violations.

## General Principles

1.  **Automate as much as possible:** Use automated tools to enforce style rules and format code.
2.  **Integrate into Workflow:** Incorporate linting and formatting checks into the development environment (IDE), pre-commit hooks, and CI/CD pipeline.
3.  **Consistency over Preference:** Once a style is chosen, adhere to it strictly, even if it differs from personal preference.

## Rust Backend (`src-tauri`)

### Formatting: `rustfmt`

*   **Purpose:** Automatically formats Rust code according to a predefined style.
*   **Usage:**
    ```bash
    # Format the entire project
    cargo fmt
    # Check if code is formatted (useful in CI)
    cargo fmt -- --check
    ```
*   **Configuration:** `rustfmt` can be configured via a `rustfmt.toml` file in the project root. We will use the default settings unless specific project needs arise.

### Linting: `clippy`

*   **Purpose:** A collection of lints to catch common mistakes, improve Rust code, and enforce best practices.
*   **Usage:**
    ```bash
    # Run clippy on the entire project
    cargo clippy
    ```
*   **Configuration:** `clippy` can be configured via `clippy.toml` or attributes in `lib.rs`/`main.rs`. We will enable most pedantic lints and address warnings as errors in CI.

### Integration

*   **IDE Integration:** Configure VS Code (with Rust Analyzer) or other IDEs to run `rustfmt` on save and display `clippy` warnings.
*   **Pre-commit Hook:** Consider adding a Git pre-commit hook to run `cargo fmt -- --check` and `cargo clippy` before committing.
*   **CI/CD Pipeline:** Include `cargo fmt -- --check` and `cargo clippy -- -D warnings` (treat warnings as errors) as mandatory steps in the CI pipeline.

## React Frontend (`assistant-ui`)

### Formatting: `Prettier`

*   **Purpose:** An opinionated code formatter that supports JavaScript, TypeScript, JSX, CSS, HTML, and more.
*   **Usage:**
    ```bash
    # Format all files
    npx prettier --write .
    # Check if files are formatted (useful in CI)
    npx prettier --check .
    ```
*   **Configuration:** `Prettier` can be configured via `.prettierrc` file. We will define a consistent set of rules (e.g., `semi: true`, `singleQuote: true`, `tabWidth: 2`).

### Linting: `ESLint`

*   **Purpose:** Statically analyzes JavaScript/TypeScript code to find problematic patterns and enforce style guidelines.
*   **Usage:**
    ```bash
    # Run ESLint on the project
    npx eslint .
    # Fix auto-fixable issues
    npx eslint --fix .
    ```
*   **Configuration:** `ESLint` is configured via `.eslintrc.js` or similar. We will use a combination of recommended rules, React-specific rules (e.g., `eslint-plugin-react`), and TypeScript-specific rules (`@typescript-eslint/eslint-plugin`).

### Integration

*   **IDE Integration:** Configure VS Code (with ESLint and Prettier extensions) to format on save and display linting errors/warnings.
*   **Pre-commit Hook:** Use `lint-staged` and `husky` to run `prettier --check` and `eslint` on staged files before committing.
*   **CI/CD Pipeline:** Include `npx prettier --check .` and `npx eslint .` as mandatory steps in the CI pipeline.

## Conclusion

By consistently applying these linting and formatting tools, we ensure a high level of code quality and maintainability across 'The Collective' project, making it easier for developers to collaborate and contribute effectively.