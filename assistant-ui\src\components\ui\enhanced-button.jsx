import React, { forwardRef, memo } from 'react';
import { Loader2 } from 'lucide-react';
import { Button } from './button';
import { cn } from '../../lib/utils';

/**
 * EnhancedButton component extending the base Button with loading states and improved icon handling
 * 
 * @param {Object} props
 * @param {string} props.variant - Button variant: 'default', 'outline', 'ghost', 'destructive', 'secondary', 'link'
 * @param {string} props.size - Button size: 'sm', 'md', 'lg', 'icon'
 * @param {boolean} props.loading - Whether to show loading state
 * @param {React.ComponentType} props.icon - Icon component to display
 * @param {string} props.iconPosition - Icon position: 'left' or 'right'
 * @param {boolean} props.disabled - Whether button is disabled
 * @param {React.ReactNode} props.children - Button content
 * @param {string} props.className - Additional CSS classes
 * @param {Function} props.onClick - Click handler
 */
export const EnhancedButton = forwardRef(({
  variant = 'default',
  size = 'default',
  loading = false,
  icon: Icon,
  iconPosition = 'left',
  disabled = false,
  children,
  className,
  onClick,
  ...props
}, ref) => {
  const isDisabled = disabled || loading;
  
  // Handle click with loading protection
  const handleClick = (e) => {
    if (loading || disabled) {
      e.preventDefault();
      return;
    }
    onClick?.(e);
  };

  return (
    <Button
      ref={ref}
      variant={variant}
      size={size}
      disabled={isDisabled}
      onClick={handleClick}
      className={cn(
        // Enhanced hover effects
        'transition-all duration-150',
        'hover:scale-[1.02] active:scale-[0.98]',
        'hover:shadow-md active:shadow-sm',
        // Loading state styles
        loading && 'cursor-wait',
        // Enhanced focus styles
        'focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
        className
      )}
      {...props}
    >
      {/* Loading Spinner */}
      {loading && (
        <Loader2 
          className={cn(
            'animate-spin',
            size === 'sm' ? 'w-3 h-3' : size === 'lg' ? 'w-5 h-5' : 'w-4 h-4'
          )}
        />
      )}
      
      {/* Left Icon */}
      {!loading && Icon && iconPosition === 'left' && (
        <Icon 
          className={cn(
            size === 'sm' ? 'w-3 h-3' : size === 'lg' ? 'w-5 h-5' : 'w-4 h-4'
          )}
        />
      )}
      
      {/* Button Content */}
      {children && (
        <span className={cn(
          'inline-block',
          loading && 'opacity-70'
        )}>
          {children}
        </span>
      )}
      
      {/* Right Icon */}
      {!loading && Icon && iconPosition === 'right' && (
        <Icon 
          className={cn(
            size === 'sm' ? 'w-3 h-3' : size === 'lg' ? 'w-5 h-5' : 'w-4 h-4'
          )}
        />
      )}
      
      {/* Screen Reader Loading Text */}
      {loading && (
        <span className="sr-only">Loading...</span>
      )}
    </Button>
  );
});

EnhancedButton.displayName = 'EnhancedButton';

export default memo(EnhancedButton);