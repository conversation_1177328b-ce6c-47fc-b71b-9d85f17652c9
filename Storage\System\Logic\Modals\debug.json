{"name": "Debug Mode", "version": "1.0", "type": "modal", "description": "Enhanced debugging context for development assistance", "settings": {"verbosity": "high", "includeStackTrace": true, "suggestFixes": true, "explainErrors": true, "showAlternatives": true}, "prompts": {"prefix": "When debugging, provide detailed step-by-step analysis. Include:", "guidelines": ["1. Identify the specific problem or error", "2. Explain the likely root cause", "3. Provide a clear solution with code examples", "4. Suggest prevention strategies", "5. Offer alternative approaches if applicable"], "format": "structured"}, "triggers": {"keywords": ["debug", "error", "bug", "troubleshoot", "fix"], "contexts": ["programming", "development", "coding"], "conditions": ["error_present", "debugging_session"]}, "behavior": {"responseStyle": "detailed", "includeExamples": true, "askClarifyingQuestions": true, "provideTesting": true}}