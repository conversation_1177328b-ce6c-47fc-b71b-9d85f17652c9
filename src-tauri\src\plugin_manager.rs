use serde::{Deserialize, Serialize};
use std::fs;
use std::env;
use crate::settings_manager::UserPreferences;

// Helper function to resolve relative paths from app directory
fn resolve_path(path: &str) -> Result<std::path::PathBuf, String> {
    if path.starts_with("./") {
        let mut current_dir = env::current_dir().map_err(|e| format!("Failed to get current directory: {}", e))?;

        // Traverse up until we find a directory containing "src-tauri"
        while !current_dir.join("src-tauri").exists() {
            if !current_dir.pop() {
                return Err("Could not find project root containing 'src-tauri'".to_string());
            }
        }

        let resolved = current_dir.join(&path[2..]);
        Ok(resolved)
    } else {
        Ok(std::path::PathBuf::from(path))
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Plugin {
    pub id: i64,
    pub name: String,
    pub description: Option<String>,
    pub version: String,
    pub enabled: bool,
    pub path: String,
    pub app_type: Option<String>,
    pub plugin_type: Option<String>,
    pub features: Option<Vec<String>>,
    pub author: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct PluginJson {
    pub name: String,
    pub description: Option<String>,
    pub version: String,
    #[serde(default = "default_enabled")]
    pub enabled: bool,
    pub main: Option<String>,
    pub class_name: Option<String>,
    pub plugin_type: Option<String>,
    pub app_type: Option<String>,
    pub features: Option<Vec<String>>,
    pub author: Option<String>,
}

fn default_enabled() -> bool {
    true
}

#[tauri::command]
pub async fn get_plugins() -> Result<Vec<Plugin>, String> {
    let prefs = UserPreferences::load();
    let plugins_dir = resolve_path(&prefs.plugins_path)?;

    // Reduced debug logging

    if !plugins_dir.exists() {
        return Err(format!("Plugins directory does not exist: {}", plugins_dir.display()));
    }

    // Reduced debug logging

    let mut plugins = Vec::new();
    let mut plugin_id = 1;

    // Scan directory directly for plugins
    for entry in fs::read_dir(&plugins_dir).map_err(|e| format!("Failed to read plugins directory: {}", e))? {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let plugin_path = entry.path();

        // Reduced debug logging

        if plugin_path.is_dir() {
            let plugin_json_path = plugin_path.join("plugin.json");
            if plugin_json_path.exists() {
                match fs::read_to_string(&plugin_json_path) {
                    Ok(contents) => {
                        match serde_json::from_str::<PluginJson>(&contents) {
                            Ok(plugin_data) => {
                                plugins.push(Plugin {
                                    id: plugin_id,
                                    name: plugin_data.name.clone(),
                                    description: plugin_data.description.or_else(|| Some("No description".to_string())),
                                    version: plugin_data.version,
                                    enabled: plugin_data.enabled,
                                    path: plugin_path.to_string_lossy().to_string(),
                                    app_type: plugin_data.app_type.or_else(|| Some("app".to_string())),
                                    plugin_type: plugin_data.plugin_type.or_else(|| Some("ui_component".to_string())),
                                    features: plugin_data.features,
                                    author: plugin_data.author,
                                });
                                plugin_id += 1;
                            }
                            Err(e) => {
                                eprintln!("Failed to parse plugin.json in {}: {}", plugin_path.display(), e);
                            }
                        }
                    }
                    Err(e) => {
                        eprintln!("Failed to read plugin.json in {}: {}", plugin_path.display(), e);
                    }
                }
            }
        }
    }

    println!("DEBUG: Total plugins found: {}", plugins.len());
    Ok(plugins)
}

#[tauri::command]
pub async fn toggle_plugin(plugin_name: String, enabled: bool) -> Result<String, String> {
    let prefs = UserPreferences::load();
    let plugins_dir = resolve_path(&prefs.plugins_path)?;

    if !plugins_dir.exists() {
        return Err(format!("Plugins directory does not exist: {}", plugins_dir.display()));
    }

    // Find the plugin directory
    for entry in fs::read_dir(&plugins_dir).map_err(|e| format!("Failed to read plugins directory: {}", e))? {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let plugin_path = entry.path();

        if plugin_path.is_dir() {
            let plugin_json_path = plugin_path.join("plugin.json");
            if plugin_json_path.exists() {
                let contents = fs::read_to_string(&plugin_json_path)
                    .map_err(|e| format!("Failed to read plugin.json: {}", e))?;
                let mut plugin_data: PluginJson = serde_json::from_str(&contents)
                    .map_err(|e| format!("Failed to parse plugin.json: {}", e))?;

                if plugin_data.name == plugin_name {
                    // Toggle the enabled state
                    plugin_data.enabled = enabled;

                    // Write back to file
                    let updated_contents = serde_json::to_string_pretty(&plugin_data)
                        .map_err(|e| format!("Failed to serialize plugin.json: {}", e))?;
                    fs::write(&plugin_json_path, updated_contents)
                        .map_err(|e| format!("Failed to write plugin.json: {}", e))?;

                    return Ok(format!("Plugin {} {}", plugin_name, if enabled { "enabled" } else { "disabled" }));
                }
            }
        }
    }

    Err(format!("Plugin {} not found", plugin_name))
}

#[tauri::command]
pub async fn refresh_plugins() -> Result<Vec<Plugin>, String> {
    // Just call get_plugins since it scans the filesystem directly
    get_plugins().await
}

#[tauri::command]
pub async fn save_plugin_state(plugin_name: String, state_data: String) -> Result<String, String> {
    let prefs = UserPreferences::load();
    let plugins_dir = resolve_path(&prefs.plugins_path)?;
    let plugin_dir = plugins_dir.join(&plugin_name);
    let data_dir = plugin_dir.join("data");
    
    // Create data directory if it doesn't exist
    if !data_dir.exists() {
        fs::create_dir_all(&data_dir)
            .map_err(|e| format!("Failed to create data directory: {}", e))?;
    }
    
    let state_file = data_dir.join("state.json");
    fs::write(&state_file, state_data)
        .map_err(|e| format!("Failed to save plugin state: {}", e))?;
    
    Ok("State saved successfully".to_string())
}

#[tauri::command]
pub async fn load_plugin_state(plugin_name: String) -> Result<String, String> {
    let prefs = UserPreferences::load();
    let plugins_dir = resolve_path(&prefs.plugins_path)?;
    let plugin_dir = plugins_dir.join(&plugin_name);
    let state_file = plugin_dir.join("data").join("state.json");
    
    if state_file.exists() {
        fs::read_to_string(&state_file)
            .map_err(|e| format!("Failed to load plugin state: {}", e))
    } else {
        Ok("{}".to_string())
    }
}

#[tauri::command]
pub async fn backup_plugin_data(plugin_name: String) -> Result<String, String> {
    let prefs = UserPreferences::load();
    let plugins_dir = resolve_path(&prefs.plugins_path)?;
    let plugin_dir = plugins_dir.join(&plugin_name);
    let data_dir = plugin_dir.join("data");
    let backup_dir = plugin_dir.join("backups");
    
    if !data_dir.exists() {
        return Err("No data directory found to backup".to_string());
    }
    
    // Create backup directory if it doesn't exist
    if !backup_dir.exists() {
        fs::create_dir_all(&backup_dir)
            .map_err(|e| format!("Failed to create backup directory: {}", e))?;
    }
    
    // Create timestamp for backup
    let timestamp = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_secs();
    
    let backup_name = format!("backup_{}.tar", timestamp);
    let backup_path = backup_dir.join(&backup_name);
    
    // Simple backup - just copy the entire data directory
    // In a real implementation, you might want to use proper archiving
    let backup_data_dir = backup_dir.join(format!("data_{}", timestamp));
    copy_dir_all(&data_dir, &backup_data_dir)
        .map_err(|e| format!("Failed to backup data: {}", e))?;
    
    Ok(format!("Backup created: {}", backup_name))
}

// Helper function to copy directories recursively
fn copy_dir_all(src: &std::path::Path, dst: &std::path::Path) -> std::io::Result<()> {
    fs::create_dir_all(dst)?;
    for entry in fs::read_dir(src)? {
        let entry = entry?;
        let ty = entry.file_type()?;
        if ty.is_dir() {
            copy_dir_all(&entry.path(), &dst.join(entry.file_name()))?;
        } else {
            fs::copy(entry.path(), dst.join(entry.file_name()))?;
        }
    }
    Ok(())
}

#[tauri::command]
pub async fn get_plugin_settings(plugin_name: String) -> Result<String, String> {
    let prefs = UserPreferences::load();
    let plugins_dir = resolve_path(&prefs.plugins_path)?;
    let plugin_dir = plugins_dir.join(&plugin_name);
    let settings_file = plugin_dir.join("data").join("settings.json");
    
    if settings_file.exists() {
        fs::read_to_string(&settings_file)
            .map_err(|e| format!("Failed to read plugin settings: {}", e))
    } else {
        Ok("{}".to_string())
    }
}

#[tauri::command]
pub async fn save_plugin_data(plugin_name: String, data_key: String, data_value: String) -> Result<String, String> {
    let prefs = UserPreferences::load();
    let plugins_dir = resolve_path(&prefs.plugins_path)?;
    let plugin_dir = plugins_dir.join(&plugin_name);
    let data_dir = plugin_dir.join("data");
    
    // Create data directory if it doesn't exist
    if !data_dir.exists() {
        fs::create_dir_all(&data_dir)
            .map_err(|e| format!("Failed to create data directory: {}", e))?;
    }
    
    let data_file = data_dir.join(format!("{}.json", data_key));
    fs::write(&data_file, data_value)
        .map_err(|e| format!("Failed to save plugin data: {}", e))?;
    
    Ok("Data saved successfully".to_string())
}

#[tauri::command]
pub async fn get_plugin_data(plugin_name: String, data_key: String) -> Result<String, String> {
    let prefs = UserPreferences::load();
    let plugins_dir = resolve_path(&prefs.plugins_path)?;
    let plugin_dir = plugins_dir.join(&plugin_name);
    let data_file = plugin_dir.join("data").join(format!("{}.json", data_key));
    
    if data_file.exists() {
        fs::read_to_string(&data_file)
            .map_err(|e| format!("Failed to read plugin data: {}", e))
    } else {
        Err("Data file not found".to_string())
    }
}

#[tauri::command]
pub async fn debug_plugin_loading() -> Result<String, String> {
    let mut debug_info = String::new();

    // Get plugins path from user preferences
    let prefs = UserPreferences::load();
    debug_info.push_str(&format!("Plugins path (from preferences): {}\n", prefs.plugins_path));

    // Resolve relative paths to absolute paths
    let plugins_dir = resolve_path(&prefs.plugins_path)?;

    debug_info.push_str(&format!("Plugins path (resolved): {}\n", plugins_dir.display()));
    debug_info.push_str(&format!("Directory exists: {}\n", plugins_dir.exists()));

    if plugins_dir.exists() {
        match fs::read_dir(&plugins_dir) {
            Ok(entries) => {
                debug_info.push_str("Directory contents:\n");
                for entry in entries {
                    match entry {
                        Ok(entry) => {
                            let path = entry.path();
                            debug_info.push_str(&format!("  - {} ({})\n",
                                path.file_name().unwrap_or_default().to_string_lossy(),
                                if path.is_dir() { "directory" } else { "file" }
                            ));

                            if path.is_dir() {
                                let plugin_json = path.join("plugin.json");
                                debug_info.push_str(&format!("    plugin.json exists: {}\n", plugin_json.exists()));
                                if plugin_json.exists() {
                                    match fs::read_to_string(&plugin_json) {
                                        Ok(content) => {
                                            debug_info.push_str(&format!("    plugin.json content: {}\n", content));
                                        }
                                        Err(e) => {
                                            debug_info.push_str(&format!("    Error reading plugin.json: {}\n", e));
                                        }
                                    }
                                }
                            }
                        }
                        Err(e) => {
                            debug_info.push_str(&format!("  Error reading entry: {}\n", e));
                        }
                    }
                }
            }
            Err(e) => {
                debug_info.push_str(&format!("Error reading directory: {}\n", e));
            }
        }
    }

    Ok(debug_info)
}


