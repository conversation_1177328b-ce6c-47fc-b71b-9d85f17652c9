import React, { useEffect, useState } from 'react';
import { invoke } from '@tauri-apps/api/core';

const ModelLoader = () => {
  const [models, setModels] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const loadModels = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('🔄 Loading models after app initialization...');
      const availableModels = await invoke('get_models_command');
      console.log('✅ Models loaded successfully:', availableModels);
      setModels(availableModels);
    } catch (err) {
      console.error('❌ Failed to load models:', err);
      setError(err.toString());
    } finally {
      setLoading(false);
    }
  };

  // Auto-load models when component mounts (after app initialization)
  useEffect(() => {
    // Wait a bit to ensure server is fully ready
    const timer = setTimeout(() => {
      loadModels();
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  // Don't render anything - this is just a background loader
  return null;
};

export default ModelLoader;
