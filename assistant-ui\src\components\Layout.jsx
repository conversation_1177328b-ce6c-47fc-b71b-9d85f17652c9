import React from 'react';
import { useLocation } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faRobot } from '@fortawesome/free-solid-svg-icons';
import TabSystem from './TabSystem.jsx';
import StatusBar from './StatusBar.jsx';
import FloatingChat from './FloatingChat.jsx';

const Layout = ({ children }) => {
  const location = useLocation();
  const isChatPage = location.pathname === '/';

  return (
    <div className="flex flex-col bg-background text-foreground min-h-screen">
      {/* Header */}
      <header className="flex items-center justify-center px-2 py-1 bg-slate-100 dark:bg-slate-900 border-b border-slate-200 dark:border-slate-700 h-6">
        <div className="flex items-center space-x-1">
          <div className="bg-gradient-to-r from-blue-600 to-purple-700 p-1 rounded-sm">
            <FontAwesomeIcon icon={faRobot} className="h-3 w-3 text-white" />
          </div>
          <div>
            <h1 className="font-medium text-xs bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              The Collective
            </h1>
          </div>
        </div>
      </header>

      {/* Tab System */}
      <div className="flex-1 flex flex-col">
        <TabSystem>
          {children}
        </TabSystem>
      </div>

      {/* Floating Chat System for Non-Chat Pages */}
      {!isChatPage && (
        <FloatingChat />
      )}

      {/* VS Code Style Status Bar - Always at bottom */}
      <StatusBar />
    </div>
  );
};

export default Layout;