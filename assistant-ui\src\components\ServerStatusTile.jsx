import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import LiveTile from './ui/live-tile';
import StatusIndicator from './ui/status-indicator';
import EnhancedButton from './ui/enhanced-button';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faServer, 
  faPowerOff, 
  faSync, 
  faCheckCircle, 
  faExclamationCircle,
  faPlay,
  faStop,
  faWifi,
  faClock
} from '@fortawesome/free-solid-svg-icons';
import { cn } from '../lib/utils';

/**
 * Enhanced ServerStatusTile component with real-time monitoring and modern UI
 */
const ServerStatusTile = ({ 
  size = 'medium',
  onClick,
  className,
  showControls = true,
  autoRefresh = true,
  refreshInterval = 5000,
  ...props 
}) => {
  const [serverStatus, setServerStatus] = useState('unknown');
  const [connectionLatency, setConnectionLatency] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isToggling, setIsToggling] = useState(false);
  const [lastCheck, setLastCheck] = useState(null);
  const [errorMessage, setErrorMessage] = useState(null);
  const [uptime, setUptime] = useState('0m');

  // Real-time server status monitoring
  const checkServerStatus = async () => {
    const startTime = Date.now();
    
    try {
      setErrorMessage(null);
      const status = await invoke('get_server_status');
      
      // Calculate connection latency
      const latency = Date.now() - startTime;
      setConnectionLatency(latency);
      
      setServerStatus(status.status || 'unknown');
      setLastCheck(new Date());
      
      // Mock uptime calculation (replace with real data from backend)
      if (status.status === 'running') {
        const uptimeMs = Date.now() - (status.startTime || Date.now() - 300000);
        const minutes = Math.floor(uptimeMs / 60000);
        const hours = Math.floor(minutes / 60);
        
        if (hours > 0) {
          setUptime(`${hours}h ${minutes % 60}m`);
        } else {
          setUptime(`${minutes}m`);
        }
      } else {
        setUptime('0m');
      }
      
    } catch (error) {
      console.error('Failed to get server status:', error);
      setServerStatus('error');
      setErrorMessage(error.message || 'Connection failed');
      setConnectionLatency(null);
    }
  };

  // Auto-refresh with visibility detection
  useEffect(() => {
    if (!autoRefresh) return;

    const performCheck = () => {
      if (document.visibilityState === 'visible' && !isToggling) {
        checkServerStatus();
      }
    };

    // Initial check
    performCheck();

    // Set up interval
    const interval = setInterval(performCheck, refreshInterval);

    // Listen for visibility changes
    document.addEventListener('visibilitychange', performCheck);

    return () => {
      clearInterval(interval);
      document.removeEventListener('visibilitychange', performCheck);
    };
  }, [autoRefresh, refreshInterval, isToggling]);

  // Server control functions
  const handleToggleServer = async () => {
    setIsToggling(true);
    setIsLoading(true);
    
    try {
      if (serverStatus === 'running') {
        await invoke('stop_server_command');
      } else {
        await invoke('start_server_command');
      }
      
      // Wait a bit for server to start/stop, then check status
      setTimeout(async () => {
        await checkServerStatus();
        setIsToggling(false);
        setIsLoading(false);
      }, 2000);
      
    } catch (error) {
      console.error('Failed to toggle server:', error);
      setErrorMessage(error.message || 'Toggle operation failed');
      setIsToggling(false);
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    await checkServerStatus();
    setIsLoading(false);
  };

  // Status mapping for StatusIndicator
  const getStatusIndicatorStatus = () => {
    switch (serverStatus) {
      case 'running': return 'online';
      case 'stopped': return 'offline';
      case 'starting': return 'loading';
      case 'stopping': return 'loading';
      case 'error': return 'error';
      default: return 'idle';
    }
  };

  // Get display text for server status
  const getDisplayStatus = () => {
    switch (serverStatus) {
      case 'running': return 'Online';
      case 'stopped': return 'Offline';
      case 'starting': return 'Starting...';
      case 'stopping': return 'Stopping...';
      case 'error': return 'Error';
      default: return 'Unknown';
    }
  };

  // Get connection quality trend
  const getConnectionTrend = () => {
    if (!connectionLatency) return null;
    
    if (connectionLatency < 50) {
      return { direction: 'down', value: `${connectionLatency}ms`, color: 'green' };
    } else if (connectionLatency < 200) {
      return { direction: 'neutral', value: `${connectionLatency}ms`, color: 'yellow' };
    } else {
      return { direction: 'up', value: `${connectionLatency}ms`, color: 'red' };
    }
  };

  // Build subtitle with connection info
  const getSubtitle = () => {
    if (serverStatus === 'running') {
      const parts = [`Uptime: ${uptime}`];
      if (connectionLatency) {
        parts.push(`${connectionLatency}ms`);
      }
      return parts.join(' • ');
    } else if (errorMessage) {
      return errorMessage;
    } else {
      return 'Server offline';
    }
  };

  return (
    <LiveTile
      title="AI Server"
      value={getDisplayStatus()}
      subtitle={getSubtitle()}
      size={size}
      icon={faServer}
      status={getStatusIndicatorStatus()}
      trend={getConnectionTrend()}
      onClick={onClick}
      loading={isLoading}
      className={cn('group', className)}
      {...props}
    >
      {showControls && (
        <div className="mt-4 flex items-center gap-2">
          {/* Toggle Server Button */}
          <EnhancedButton
            onClick={handleToggleServer}
            disabled={isToggling || isLoading}
            loading={isToggling}
            variant={serverStatus === 'running' ? 'destructive' : 'default'}
            size="sm"
            icon={serverStatus === 'running' ? faStop : faPlay}
            iconPosition="left"
            className="flex-1"
          >
            {serverStatus === 'running' ? 'Stop' : 'Start'}
          </EnhancedButton>

          {/* Refresh Button */}
          <EnhancedButton
            onClick={handleRefresh}
            disabled={isLoading || isToggling}
            loading={isLoading && !isToggling}
            variant="outline"
            size="sm"
            icon={faSync}
            className="px-3"
          />
        </div>
      )}

      {/* Status Details */}
      {size === 'large' && (
        <div className="mt-4 space-y-2 text-xs">
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Connection:</span>
            <div className="flex items-center gap-1">
              <StatusIndicator 
                status={connectionLatency ? 'online' : 'offline'} 
                size="sm" 
              />
              <span className="font-medium">
                {connectionLatency ? `${connectionLatency}ms` : 'N/A'}
              </span>
            </div>
          </div>
          
          {lastCheck && (
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">Last Check:</span>
              <span className="font-medium">
                {lastCheck.toLocaleTimeString()}
              </span>
            </div>
          )}
        </div>
      )}
    </LiveTile>
  );
};

export default ServerStatusTile;