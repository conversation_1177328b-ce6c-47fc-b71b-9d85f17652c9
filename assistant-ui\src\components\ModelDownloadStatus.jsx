import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from './ui/card';
import { Progress } from './ui/progress';

const ModelDownloadStatus = ({ downloadStatus }) => {
  if (!downloadStatus) {
    return null;
  }

  const { modelName, status, total, completed } = downloadStatus;
  const percent = total > 0 ? (completed / total) * 100 : 0;

  // Format bytes for display
  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Card className="mt-4 border-blue-200 bg-blue-50 dark:bg-blue-950 dark:border-blue-800">
      <CardHeader className="pb-3">
        <CardTitle className="text-blue-800 dark:text-blue-200">
          Downloading Model: {modelName}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <p className="text-sm text-blue-600 dark:text-blue-300 capitalize">{status}</p>
            <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
              {percent.toFixed(1)}%
            </p>
          </div>
          <Progress value={percent} className="mt-2" />
          {total > 0 && (
            <div className="flex items-center justify-between text-xs text-blue-600 dark:text-blue-400">
              <span>{formatBytes(completed)} / {formatBytes(total)}</span>
              <span>{((completed / total) * 100).toFixed(1)}% complete</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ModelDownloadStatus;
