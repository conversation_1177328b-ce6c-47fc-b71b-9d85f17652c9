import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFile, faBoxOpen, faRobot, faPlus, faFolder, faRefresh, faEdit, faTrash, faExclamationTriangle } from '@fortawesome/free-solid-svg-icons';
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from "./ui/card";
import { Button } from "./ui/button";

const FileManager = ({ basePath, sections, onPathSelected, title, description, enableCascadeView }) => {
  const [sectionFiles, setSectionFiles] = useState({});
  const [sectionMetadata, setSectionMetadata] = useState({});
  const [loadingFiles, setLoadingFiles] = useState(false);
  const [error, setError] = useState(null);

  // Initialize section files state
  useEffect(() => {
    if (sections) {
      const initialState = {};
      const initialMetadata = {};
      sections.forEach(section => {
        initialState[section.key] = [];
        initialMetadata[section.key] = null;
      });
      setSectionFiles(initialState);
      setSectionMetadata(initialMetadata);
    }
  }, [sections]);

  // Load files when path changes
  useEffect(() => {
    if (basePath && sections) {
      loadSectionFiles(basePath);
    }
  }, [basePath, sections]);

  const loadSectionFiles = async (path) => {
    if (!path || !sections) {
      console.log('⚠️ loadSectionFiles called but missing path or sections:', { path, sections });
      return;
    }
    
    setLoadingFiles(true);
    setError(null);
    
    try {
      console.log('🔍 Loading files from:', path);
      console.log('📁 Sections to load:', sections.map(s => ({ key: s.key, name: s.name, folderNames: s.folderNames, fileExtensions: s.fileExtensions })));
      
      // Load files from each configured section
      const sectionPromises = sections.map(section => 
        loadFilesFromFolderVariants(path, section.folderNames || [section.key], section.fileExtensions || ['.json', '.js', '.jsx'])
      );
      
      const results = await Promise.allSettled(sectionPromises);
      
      const newSectionFiles = {};
      sections.forEach((section, index) => {
        newSectionFiles[section.key] = results[index].status === 'fulfilled' ? results[index].value : [];
      });
      
      setSectionFiles(newSectionFiles);

      // Load metadata for sections that have it
      await loadSectionMetadata(path);

      console.log('✅ Loaded files:', newSectionFiles);

    } catch (error) {
      console.error('❌ Error loading files:', error);
      setError('Failed to load files. Please check the directory path.');
    } finally {
      setLoadingFiles(false);
    }
  };

  const loadFilesFromFolderVariants = async (basePath, folderNames, fileExtensions = ['.json', '.js', '.jsx']) => {
    // Extract fileExtensions from section if available
    const extensions = fileExtensions;
    
    for (const folderName of folderNames) {
      try {
        const folderPath = `${basePath}\\${folderName}`;
        console.log('🔍 Trying folder path:', folderPath);
        
        // Use browse_directory instead of the non-existent list_directory_contents
        const result = await invoke('browse_directory', { path: folderPath });
        console.log('📄 Raw browse_directory result for', folderPath, ':', result);
        
        if (result && result.files) {
          console.log('🔍 Raw files before filtering:', result.files);
          console.log('🔍 Extensions to filter for:', extensions);
          
          const files = result.files
            .filter(fileObj => {
              const fileName = fileObj.name || fileObj;
              // Skip metadata files (_index.json) from regular file listing
              if (fileName === '_index.json') {
                console.log('🚫 Skipping metadata file:', fileName);
                return false;
              }
              // TEMPORARILY SHOW ALL FILES - NO EXTENSION FILTERING
              console.log(`🔍 File ${fileName} - SHOWING ALL FILES (no filtering)`);
              return true;
            })
            .map(fileObj => {
              const fileName = fileObj.name || fileObj;
              return {
                name: fileName,
                path: fileObj.path || `${folderPath}\\${fileName}`,
                size: fileObj.size || null,
                extension: fileName.split('.').pop()
              };
            });
          
          console.log(`✅ Found ${files.length} files in ${folderPath}:`, files.map(f => f.name));
          return files;
        } else {
          console.log(`⚠️ No files returned from browse_directory for ${folderPath}:`, result);
          return [];
        }
      } catch (error) {
        console.warn(`⚠️ Could not load from ${folderName}:`, error.message);
        continue; // Try next variant
      }
    }
    return []; // Return empty if no variants work
  };

  const loadSectionMetadata = async (path) => {
    if (!path || !sections) return;
    
    try {
      const metadataPromises = sections
        .filter(section => section.hasMetadata)
        .map(async (section) => {
          for (const folderName of section.folderNames || [section.key]) {
            try {
              const folderPath = `${path}\\${folderName}`;
              const metadataPath = `${folderPath}\\_index.json`;
              
              // Try to read metadata file
              const metadataContent = await invoke('get_file_content', { file_path: metadataPath });
              const metadata = JSON.parse(metadataContent);
              
              console.log(`📋 Loaded metadata for ${section.key}:`, metadata);
              return { key: section.key, metadata };
            } catch (error) {
              console.warn(`⚠️ No metadata found for ${section.key}:`, error.message);
              continue;
            }
          }
          return { key: section.key, metadata: null };
        });
      
      const metadataResults = await Promise.allSettled(metadataPromises);
      const newMetadata = {};
      
      metadataResults.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value) {
          newMetadata[result.value.key] = result.value.metadata;
        }
      });
      
      setSectionMetadata(newMetadata);
      console.log('📋 All metadata loaded:', newMetadata);
      
    } catch (error) {
      console.error('❌ Error loading metadata:', error);
    }
  };

  const findFileInMetadata = (metadata, fileName) => {
    if (!metadata || !metadata.items) return null;
    return metadata.items.find(item => item.file === fileName);
  };

  const handleCreateNew = async (section, sectionKey) => {
    console.log(`Create new ${section.name} clicked`);
    if (section.onCreate) {
      section.onCreate(sectionKey);
    } else {
      // Default behavior
      alert(`Creating new ${section.name} - TODO: Implement file creation dialog`);
    }
  };

  const handleEditFile = async (file, section, sectionKey) => {
    console.log(`Edit ${section.name} file:`, file);
    if (section.onEdit) {
      section.onEdit(file, sectionKey);
    } else {
      // Default behavior
      alert(`Edit ${file.name} - TODO: Implement file editing`);
    }
  };

  const handleDeleteFile = async (file, section, sectionKey) => {
    if (window.confirm(`Are you sure you want to delete ${file.name}?`)) {
      try {
        console.log(`Delete ${section.name} file:`, file);
        if (section.onDelete) {
          await section.onDelete(file, sectionKey);
          // Refresh files after deletion
          await loadSectionFiles(basePath);
        } else {
          // Default behavior
          alert(`Delete ${file.name} - TODO: Implement file deletion`);
        }
      } catch (error) {
        console.error('Error deleting file:', error);
        alert('Failed to delete file');
      }
    }
  };

  const renderFileSection = (section) => {
    const files = sectionFiles[section.key] || [];
    const metadata = sectionMetadata[section.key];
    
    console.log(`🎨 Rendering section ${section.key}:`, {
      sectionName: section.name,
      filesCount: files.length,
      files: files.map(f => f.name),
      folderNames: section.folderNames,
      fileExtensions: section.fileExtensions
    });
    
    return (
      <Card key={section.key}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FontAwesomeIcon icon={section.icon} className={`h-5 w-5 text-${section.color}-500`} />
            <span>{section.name}</span>
            {section.cascadeLevel && enableCascadeView && (
              <span className={`px-2 py-1 text-xs rounded-full bg-${section.color}-100 text-${section.color}-700`}>
                Level {section.cascadeLevel}
              </span>
            )}
          </CardTitle>
          <CardDescription>
            {section.description}
            {metadata && (
              <div className="mt-2 text-xs text-muted-foreground">
                📋 {metadata.items?.length || 0} items configured | Last updated: {metadata.lastUpdated || 'Unknown'}
              </div>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Add new file button */}
            <div 
              className={`p-4 border-2 border-dashed border-gray-300 rounded-lg text-center text-gray-500 hover:border-${section.color}-400 hover:text-${section.color}-500 cursor-pointer transition-colors`}
              onClick={() => handleCreateNew(section, section.key)}
            >
              <FontAwesomeIcon icon={faPlus} className="h-8 w-8 mb-2 mx-auto" />
              <p className="text-sm">Add {section.addLabel || section.name.slice(0, -1)}</p>
              <p className="text-xs text-gray-400">{section.addDescription || `Create new ${section.key}`}</p>
            </div>
            
            {/* Display files */}
            {loadingFiles ? (
              <div className="col-span-2 text-center py-4 text-gray-500">
                <FontAwesomeIcon icon={faRefresh} className="animate-spin mr-2" />
                Loading files...
              </div>
            ) : files.length > 0 ? (
              files.map((file, index) => (
                <Card key={`${section.key}-${file.name}-${index}`} className="p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-start space-x-3">
                    <FontAwesomeIcon icon={section.icon} className={`h-4 w-4 text-${section.color}-500 mt-1`} />
                    <div className="flex-1">
                      <h4 className="font-medium text-sm">{file.name}</h4>
                      <p className="text-xs text-gray-500">{section.fileDescription || section.description}</p>
                      <div className="flex items-center space-x-2 mt-2">
                        <span className={`px-2 py-1 bg-${section.color}-100 text-${section.color}-700 text-xs rounded`}>
                          {file.extension?.toUpperCase() || 'FILE'}
                        </span>
                        {file.size && (
                          <span className="text-xs text-gray-400">{(file.size / 1024).toFixed(1)} KB</span>
                        )}
                        {metadata && findFileInMetadata(metadata, file.name) && (
                          <span className="text-xs text-green-600">✓ Configured</span>
                        )}
                      </div>
                      <div className="flex items-center space-x-2 mt-2">
                        <button 
                          className={`text-xs text-${section.color}-600 hover:text-${section.color}-800 flex items-center`}
                          onClick={() => handleEditFile(file, section, section.key)}
                        >
                          <FontAwesomeIcon icon={faEdit} className="mr-1" />
                          Edit
                        </button>
                        <button 
                          className="text-xs text-red-600 hover:text-red-800 flex items-center"
                          onClick={() => handleDeleteFile(file, section, section.key)}
                        >
                          <FontAwesomeIcon icon={faTrash} className="mr-1" />
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))
            ) : (
              <div className="col-span-2 text-center py-4 text-gray-500">
                <FontAwesomeIcon icon={faFolder} className="h-8 w-8 mb-2 opacity-50" />
                <p>No {section.key} files found</p>
                <p className="text-xs">Click "Add {section.addLabel || section.name.slice(0, -1)}" to create your first {section.key}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  if (!basePath) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <FontAwesomeIcon icon={faExclamationTriangle} className="h-12 w-12 text-yellow-500 mb-4" />
          <h3 className="text-lg font-semibold mb-2">{title || 'No Directory Selected'}</h3>
          <p className="text-muted-foreground">{description || 'Please select a directory first to manage files.'}</p>
        </CardContent>
      </Card>
    );
  }

  if (!sections || sections.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <FontAwesomeIcon icon={faExclamationTriangle} className="h-12 w-12 text-yellow-500 mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Sections Configured</h3>
          <p className="text-muted-foreground">No file sections have been configured for this directory.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center text-red-600">
              <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
              {error}
            </div>
          </CardContent>
        </Card>
      )}
      
      {sections.map(section => renderFileSection(section))}
    </div>
  );
};

export default FileManager;