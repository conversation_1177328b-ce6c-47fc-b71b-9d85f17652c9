import platform
import psutil
import GPUtil
import datetime
import logging

logger = logging.getLogger(__name__)

class SystemStatusPlugin:
    def __init__(self):
        self.name = "SystemStatusPlugin"
        self.description = "Gathers system information (CPU, RAM, GPU, etc.)."
        logger.info(f"{self.name} initialized.")

    def get_info(self):
        logger.info(f"Attempting to get system info from {self.name}.")
        try:
            # CPU Info
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_freq = psutil.cpu_freq()
        cpu_cores = psutil.cpu_count(logical=False)
        cpu_threads = psutil.cpu_count(logical=True)

        # RAM Info
        ram = psutil.virtual_memory()
        ram_total_gb = round(ram.total / (1024**3), 2)
        ram_used_bytes = ram.total - ram.available # or ram.used
        ram_used_gb = round(ram_used_bytes / (1024**3), 2)
        ram_available_gb = round(ram.available / (1024**3), 2) # Keep for completeness if desired
        ram_percent = ram.percent

        # Disk Info (for the root partition)
        disk = psutil.disk_usage('/')
        disk_total_gb = round(disk.total / (1024**3), 2)
        disk_used_gb = round(disk.used / (1024**3), 2)
        disk_percent = disk.percent

        # GPU Info
        gpu_info = []
        try:
            gpus = GPUtil.getGPUs()
            for gpu in gpus:
                gpu_info.append({
                    "id": gpu.id,
                    "name": gpu.name,
                    "load": f"{gpu.load*100:.2f}%",
                    "memory_total_gb": f"{gpu.memoryTotal / 1024:.2f}GB",
                    "memory_used_gb": f"{gpu.memoryUsed / 1024:.2f}GB",
                    "memory_free_gb": f"{gpu.memoryFree / 1024:.2f}GB",
                    "temperature_celsius": f"{gpu.temperature:.2f}°C"
                })
        except Exception as e:
            gpu_info.append({"error": f"Could not retrieve GPU info: {e}"})

        # Battery Info (if available)
        battery = psutil.sensors_battery()
        battery_info = {
            "percent": battery.percent,
            "power_plugged": battery.power_plugged,
            "secsleft": battery.secsleft
        } if battery else "N/A"

        # System Uptime
        boot_time_timestamp = psutil.boot_time()
        boot_time = datetime.datetime.fromtimestamp(boot_time_timestamp).strftime("%Y-%m-%d %H:%M:%S")
        uptime_seconds = (datetime.datetime.now() - datetime.datetime.fromtimestamp(boot_time_timestamp)).total_seconds()
        uptime_formatted = str(datetime.timedelta(seconds=int(uptime_seconds)))

        return {
            "system": platform.system(),
            "node_name": platform.node(),
            "release": platform.release(),
            "version": platform.version(),
            "machine": platform.machine(),
            "processor": platform.processor(),
            "cpu": {
                "usage_percent": cpu_percent,
                "frequency_mhz": f"{cpu_freq.current:.2f}",
                "physical_cores": cpu_cores,
                "logical_cores": cpu_threads
            },
            "ram": {
                "total_gb": ram_total_gb,
                "used_gb": ram_used_gb,
                "available_gb": ram_available_gb, # Retained for potential other uses
                "usage_percent": ram_percent
            },
            "disk": {
                "total_gb": disk_total_gb,
                "used_gb": disk_used_gb,
                "usage_percent": disk_percent
            },
            "gpu": gpu_info,
            "battery": battery_info,
            "uptime": {
                "boot_time": boot_time,
                "formatted": uptime_formatted,
                "seconds": int(uptime_seconds)
            }
        }
        except Exception as e:
            logger.error(f"Error in {self.name}.get_info: {str(e)}", exc_info=True)
            # Return a dictionary indicating error, or re-raise, or return partial data
            return {"error": f"Failed to retrieve system metrics: {str(e)}"}

if __name__ == "__main__":
    import json # Added import for json.dumps
    # Configure basic logging for standalone testing
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    plugin = SystemStatusPlugin()
    info = plugin.get_info()
    print(json.dumps(info, indent=4))