# Notes Plugin for The Collective

import os
import json
from datetime import datetime

NAME = "Notes Plugin"
DESCRIPTION = "Create and manage simple notes"
VERSION = "0.1.0"

# Storage path for notes
STORAGE_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "storage", "notes")

# Ensure notes directory exists
os.makedirs(STORAGE_PATH, exist_ok=True)

def create_note(title, content):
    """
    Create a new note with title and content
    """
    note_id = datetime.now().strftime("%Y%m%d%H%M%S")
    timestamp = datetime.now().isoformat()
    
    note = {
        "id": note_id,
        "title": title,
        "content": content,
        "created_at": timestamp,
        "updated_at": timestamp
    }
    
    # Save note to file
    note_path = os.path.join(STORAGE_PATH, f"{note_id}.json")
    with open(note_path, 'w') as f:
        json.dump(note, f, indent=2)
    
    return note

def get_note(note_id):
    """
    Retrieve a note by ID
    """
    note_path = os.path.join(STORAGE_PATH, f"{note_id}.json")
    
    if not os.path.exists(note_path):
        return None
    
    with open(note_path, 'r') as f:
        return json.load(f)

def update_note(note_id, title=None, content=None):
    """
    Update an existing note
    """
    note_path = os.path.join(STORAGE_PATH, f"{note_id}.json")
    
    if not os.path.exists(note_path):
        return None
    
    with open(note_path, 'r') as f:
        note = json.load(f)
    
    if title is not None:
        note["title"] = title
    
    if content is not None:
        note["content"] = content
    
    note["updated_at"] = datetime.now().isoformat()
    
    with open(note_path, 'w') as f:
        json.dump(note, f, indent=2)
    
    return note

def delete_note(note_id):
    """
    Delete a note by ID
    """
    note_path = os.path.join(STORAGE_PATH, f"{note_id}.json")
    
    if not os.path.exists(note_path):
        return False
    
    os.remove(note_path)
    return True

def list_notes():
    """
    List all available notes
    """
    notes = []
    
    if os.path.exists(STORAGE_PATH):
        for filename in os.listdir(STORAGE_PATH):
            if filename.endswith(".json"):
                note_path = os.path.join(STORAGE_PATH, filename)
                with open(note_path, 'r') as f:
                    note = json.load(f)
                    notes.append({
                        "id": note["id"],
                        "title": note["title"],
                        "created_at": note["created_at"],
                        "updated_at": note["updated_at"]
                    })
    
    return sorted(notes, key=lambda x: x["updated_at"], reverse=True)

# Register plugin functions
def register_functions():
    return {
        "create_note": create_note,
        "get_note": get_note,
        "update_note": update_note,
        "delete_note": delete_note,
        "list_notes": list_notes
    }